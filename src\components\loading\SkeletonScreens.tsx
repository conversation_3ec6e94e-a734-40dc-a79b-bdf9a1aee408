import React from 'react';
import { View, StyleSheet } from 'react-native';
import { ShimmerView } from '../animations/AnimatedComponents';
import { COLORS, SPACING, BORDER_RADIUS } from '@/constants';

// Home Screen Skeleton
export const HomeScreenSkeleton: React.FC = () => {
  return (
    <View style={styles.container}>
      {/* Header Skeleton */}
      <View style={styles.headerSkeleton}>
        <ShimmerView width={200} height={32} borderRadius={8} />
        <ShimmerView width={300} height={20} borderRadius={6} style={{ marginTop: 8 }} />
      </View>

      {/* Stats Cards Skeleton */}
      <View style={styles.statsContainer}>
        <View style={styles.statsRow}>
          <ShimmerView width={160} height={100} borderRadius={16} />
          <ShimmerView width={160} height={100} borderRadius={16} />
        </View>
        <View style={styles.statsRow}>
          <ShimmerView width={160} height={100} borderRadius={16} />
          <ShimmerView width={160} height={100} borderRadius={16} />
        </View>
      </View>

      {/* Primary Action Skeleton */}
      <View style={styles.primaryActionSkeleton}>
        <ShimmerView width={350} height={200} borderRadius={20} />
      </View>

      {/* Navigation Cards Skeleton */}
      <View style={styles.navigationSkeleton}>
        <ShimmerView width={100} height={20} borderRadius={6} style={{ marginBottom: 16 }} />
        <View style={styles.navigationRow}>
          <ShimmerView width={160} height={120} borderRadius={16} />
          <ShimmerView width={160} height={120} borderRadius={16} />
        </View>
      </View>

      {/* Recent Quizzes Skeleton */}
      <View style={styles.recentSkeleton}>
        <ShimmerView width={150} height={20} borderRadius={6} style={{ marginBottom: 16 }} />
        <ShimmerView width={350} height={80} borderRadius={12} style={{ marginBottom: 12 }} />
        <ShimmerView width={350} height={80} borderRadius={12} style={{ marginBottom: 12 }} />
        <ShimmerView width={350} height={80} borderRadius={12} />
      </View>
    </View>
  );
};

// History Screen Skeleton
export const HistoryScreenSkeleton: React.FC = () => {
  return (
    <View style={styles.container}>
      {/* Analytics Cards Skeleton */}
      <View style={styles.analyticsContainer}>
        <View style={styles.analyticsRow}>
          <ShimmerView width={120} height={120} borderRadius={20} />
          <ShimmerView width={120} height={120} borderRadius={20} />
          <ShimmerView width={120} height={120} borderRadius={20} />
          <ShimmerView width={120} height={120} borderRadius={20} />
        </View>
      </View>

      {/* History List Skeleton */}
      <View style={styles.historyListSkeleton}>
        {Array.from({ length: 6 }).map((_, index) => (
          <View key={index} style={styles.historyItemSkeleton}>
            <ShimmerView width={350} height={100} borderRadius={12} />
          </View>
        ))}
      </View>
    </View>
  );
};

// Chat Screen Skeleton
export const ChatScreenSkeleton: React.FC = () => {
  return (
    <View style={styles.container}>
      {/* Chat Header Skeleton */}
      <View style={styles.chatHeaderSkeleton}>
        <ShimmerView width={150} height={24} borderRadius={6} />
        <View style={styles.chatHeaderActions}>
          <ShimmerView width={40} height={40} borderRadius={20} />
          <ShimmerView width={40} height={40} borderRadius={20} />
        </View>
      </View>

      {/* Chat Messages Skeleton */}
      <View style={styles.chatMessagesSkeleton}>
        {/* AI Message */}
        <View style={styles.aiMessageSkeleton}>
          <ShimmerView width={250} height={60} borderRadius={20} />
        </View>

        {/* User Message */}
        <View style={styles.userMessageSkeleton}>
          <ShimmerView width={200} height={40} borderRadius={20} />
        </View>

        {/* AI Message */}
        <View style={styles.aiMessageSkeleton}>
          <ShimmerView width={280} height={80} borderRadius={20} />
        </View>

        {/* User Message */}
        <View style={styles.userMessageSkeleton}>
          <ShimmerView width={180} height={40} borderRadius={20} />
        </View>

        {/* Typing Indicator */}
        <View style={styles.aiMessageSkeleton}>
          <ShimmerView width={100} height={40} borderRadius={20} />
        </View>
      </View>

      {/* Input Area Skeleton */}
      <View style={styles.chatInputSkeleton}>
        <ShimmerView width={300} height={48} borderRadius={24} />
        <ShimmerView width={48} height={48} borderRadius={24} />
      </View>
    </View>
  );
};

// Settings Screen Skeleton
export const SettingsScreenSkeleton: React.FC = () => {
  return (
    <View style={styles.container}>
      {/* Section Headers */}
      {Array.from({ length: 4 }).map((_, sectionIndex) => (
        <View key={sectionIndex} style={styles.settingsSectionSkeleton}>
          <ShimmerView width={350} height={50} borderRadius={12} style={{ marginBottom: 16 }} />
          
          {/* Section Items */}
          {Array.from({ length: 3 }).map((_, itemIndex) => (
            <View key={itemIndex} style={styles.settingsItemSkeleton}>
              <ShimmerView width={350} height={60} borderRadius={8} />
            </View>
          ))}
        </View>
      ))}
    </View>
  );
};

// Quiz Setup Screen Skeleton
export const QuizSetupScreenSkeleton: React.FC = () => {
  return (
    <View style={styles.container}>
      {/* File Info Skeleton */}
      <View style={styles.fileInfoSkeleton}>
        <ShimmerView width={350} height={120} borderRadius={12} />
      </View>

      {/* Form Fields Skeleton */}
      <View style={styles.formFieldsSkeleton}>
        <ShimmerView width={100} height={20} borderRadius={6} style={{ marginBottom: 8 }} />
        <ShimmerView width={350} height={48} borderRadius={8} style={{ marginBottom: 20 }} />

        <ShimmerView width={120} height={20} borderRadius={6} style={{ marginBottom: 8 }} />
        <ShimmerView width={350} height={48} borderRadius={8} style={{ marginBottom: 20 }} />

        <ShimmerView width={80} height={20} borderRadius={6} style={{ marginBottom: 8 }} />
        <ShimmerView width={350} height={48} borderRadius={8} style={{ marginBottom: 20 }} />
      </View>

      {/* Action Buttons Skeleton */}
      <View style={styles.actionButtonsSkeleton}>
        <ShimmerView width={160} height={48} borderRadius={8} />
        <ShimmerView width={160} height={48} borderRadius={8} />
      </View>
    </View>
  );
};

// Generic Card Skeleton
interface CardSkeletonProps {
  width?: number;
  height?: number;
  borderRadius?: number;
}

export const CardSkeleton: React.FC<CardSkeletonProps> = ({
  width = 350,
  height = 100,
  borderRadius = 12,
}) => {
  return (
    <View style={styles.cardSkeleton}>
      <ShimmerView width={width} height={height} borderRadius={borderRadius} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    padding: SPACING.lg,
  },
  // Header Skeletons
  headerSkeleton: {
    alignItems: 'center',
    marginBottom: SPACING['3xl'],
  },
  chatHeaderSkeleton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.lg,
    marginBottom: SPACING.xl,
  },
  chatHeaderActions: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  // Stats and Analytics
  statsContainer: {
    marginBottom: SPACING['3xl'],
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  analyticsContainer: {
    marginBottom: SPACING.xl,
  },
  analyticsRow: {
    flexDirection: 'row',
    gap: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  // Primary Action
  primaryActionSkeleton: {
    alignItems: 'center',
    marginBottom: SPACING['3xl'],
  },
  // Navigation
  navigationSkeleton: {
    marginBottom: SPACING['3xl'],
  },
  navigationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  // Recent/History
  recentSkeleton: {
    marginBottom: SPACING.xl,
  },
  historyListSkeleton: {
    flex: 1,
  },
  historyItemSkeleton: {
    marginBottom: SPACING.md,
  },
  // Chat
  chatMessagesSkeleton: {
    flex: 1,
    paddingVertical: SPACING.lg,
  },
  aiMessageSkeleton: {
    alignItems: 'flex-start',
    marginBottom: SPACING.lg,
  },
  userMessageSkeleton: {
    alignItems: 'flex-end',
    marginBottom: SPACING.lg,
  },
  chatInputSkeleton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md,
    paddingVertical: SPACING.lg,
  },
  // Settings
  settingsSectionSkeleton: {
    marginBottom: SPACING['3xl'],
  },
  settingsItemSkeleton: {
    marginBottom: SPACING.md,
  },
  // Quiz Setup
  fileInfoSkeleton: {
    marginBottom: SPACING.xl,
  },
  formFieldsSkeleton: {
    marginBottom: SPACING.xl,
  },
  actionButtonsSkeleton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  // Generic
  cardSkeleton: {
    marginBottom: SPACING.md,
  },
});
