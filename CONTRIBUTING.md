# Contributing to Test Me (اختبرني)

Thank you for your interest in contributing to Test Me! This document provides guidelines and information for contributors.

## 🤝 Code of Conduct

We are committed to providing a welcoming and inclusive environment for all contributors. Please read and follow our [Code of Conduct](CODE_OF_CONDUCT.md).

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Git
- Expo CLI
- iOS Simulator or Android Emulator

### Development Setup

1. **Fork the repository**
   ```bash
   git clone https://github.com/yourusername/test-me-app.git
   cd test-me-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment**
   ```bash
   cp .env.example .env
   # Add your API keys to .env
   ```

4. **Start development server**
   ```bash
   npm start
   ```

## 📋 Development Guidelines

### Code Style

We follow strict coding standards to maintain code quality:

- **TypeScript**: All code must be written in TypeScript with strict type checking
- **ESLint**: Follow the configured ESLint rules
- **Prettier**: Code formatting is enforced via Prettier
- **Naming Conventions**:
  - Components: `PascalCase` (e.g., `HomeScreen.tsx`)
  - Services: `camelCase` (e.g., `geminiService.ts`)
  - Types: `PascalCase` (e.g., `Quiz`, `Question`)
  - Constants: `UPPER_SNAKE_CASE`
  - Files: Use descriptive names with proper extensions

### Architecture Principles

1. **Separation of Concerns**: Keep business logic separate from UI components
2. **Reusability**: Create reusable components and utilities
3. **Type Safety**: Use TypeScript for all code with proper type definitions
4. **Performance**: Consider performance implications of all changes
5. **Accessibility**: Ensure all UI components are accessible
6. **Internationalization**: Support for multiple languages and RTL layouts

### Component Guidelines

#### UI Components
```typescript
// Good: Proper TypeScript interface
interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  disabled?: boolean;
}

export const Button: React.FC<ButtonProps> = ({ 
  title, 
  onPress, 
  variant = 'primary',
  disabled = false 
}) => {
  // Implementation
};
```

#### Service Classes
```typescript
// Good: Static methods with proper error handling
export class AnalyticsService {
  static async trackEvent(
    event: AnalyticsEvent,
    properties?: Record<string, any>
  ): Promise<void> {
    try {
      // Implementation
    } catch (error) {
      await ErrorHandler.handleError(error, {
        context: 'AnalyticsService.trackEvent',
        event,
        properties,
      });
    }
  }
}
```

### Testing Requirements

All contributions must include appropriate tests:

#### Unit Tests
- Test all utility functions
- Test component logic
- Test service methods
- Aim for >80% code coverage

#### Component Tests
```typescript
import { render, fireEvent } from '@testing-library/react-native';
import { Button } from '../Button';

describe('Button Component', () => {
  it('should call onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <Button title="Test" onPress={mockOnPress} />
    );
    
    fireEvent.press(getByText('Test'));
    expect(mockOnPress).toHaveBeenCalled();
  });
});
```

#### Service Tests
```typescript
import { AnalyticsService } from '../analyticsService';

describe('AnalyticsService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should track events correctly', async () => {
    await AnalyticsService.trackEvent('test_event', { key: 'value' });
    // Assertions
  });
});
```

## 🔄 Development Workflow

### Branch Strategy

We use a feature branch workflow:

1. **main**: Production-ready code
2. **develop**: Integration branch for features
3. **feature/**: Feature development branches
4. **hotfix/**: Critical bug fixes
5. **release/**: Release preparation branches

### Commit Messages

We follow [Conventional Commits](https://www.conventionalcommits.org/):

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(chat): add typing indicator animation
fix(quiz): resolve score calculation bug
docs(readme): update installation instructions
style(home): improve button spacing
refactor(services): extract common error handling
test(utils): add tests for date formatting
chore(deps): update expo to latest version
```

### Pull Request Process

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Write code following our guidelines
   - Add tests for new functionality
   - Update documentation if needed

3. **Test Your Changes**
   ```bash
   npm test
   npm run lint
   npm run type-check
   ```

4. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat(scope): your descriptive message"
   ```

5. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

6. **PR Requirements**
   - Clear description of changes
   - Link to related issues
   - Screenshots for UI changes
   - All tests passing
   - Code review approval

### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Manual testing completed
- [ ] All tests passing

## Screenshots (if applicable)
Add screenshots for UI changes

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
```

## 🐛 Bug Reports

When reporting bugs, please include:

1. **Environment Information**
   - Device/OS version
   - App version
   - React Native/Expo version

2. **Steps to Reproduce**
   - Clear, numbered steps
   - Expected vs actual behavior
   - Screenshots/videos if helpful

3. **Additional Context**
   - Error messages
   - Console logs
   - Related issues

## 💡 Feature Requests

For feature requests, please provide:

1. **Problem Description**
   - What problem does this solve?
   - Who would benefit?

2. **Proposed Solution**
   - Detailed description
   - Alternative solutions considered
   - Implementation ideas

3. **Additional Context**
   - Mockups/wireframes
   - Related features
   - Priority level

## 📚 Documentation

### Code Documentation

- Use JSDoc comments for functions and classes
- Include examples for complex APIs
- Document component props with TypeScript interfaces
- Add inline comments for complex logic

### README Updates

When adding new features:
- Update feature list
- Add configuration instructions
- Include usage examples
- Update screenshots if needed

## 🎨 Design Guidelines

### UI/UX Principles

1. **Consistency**: Follow established design patterns
2. **Accessibility**: Support screen readers and keyboard navigation
3. **Performance**: Smooth animations and fast interactions
4. **Internationalization**: Support RTL layouts and multiple languages
5. **Mobile-First**: Optimize for mobile devices

### Design System

- Use design tokens from `src/constants/`
- Follow spacing scale: `SPACING.xs` to `SPACING['3xl']`
- Use semantic colors: `COLORS.primary`, `COLORS.secondary`, etc.
- Apply consistent border radius: `BORDER_RADIUS.sm` to `BORDER_RADIUS.xl`
- Use shadow system: `SHADOWS.sm` to `SHADOWS.xl`

## 🚀 Release Process

### Version Numbering

We follow [Semantic Versioning](https://semver.org/):
- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Checklist

1. Update version in `package.json`
2. Update `CHANGELOG.md`
3. Run full test suite
4. Update documentation
5. Create release tag
6. Deploy to app stores

## 🆘 Getting Help

- **Documentation**: Check existing docs first
- **Issues**: Search existing issues before creating new ones
- **Discussions**: Use GitHub Discussions for questions
- **Discord**: Join our community Discord server
- **Email**: Contact <NAME_EMAIL>

## 🏆 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Annual contributor highlights
- Special badges for significant contributions

Thank you for contributing to Test Me! 🎉
