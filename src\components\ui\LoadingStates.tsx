import React from 'react';
import { View, Text, StyleSheet, Animated, Easing } from 'react-native';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '@/constants';
import { useTheme } from '@/contexts/ThemeContext';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'medium', 
  color 
}) => {
  const { theme } = useTheme();
  const spinValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    const spin = () => {
      spinValue.setValue(0);
      Animated.timing(spinValue, {
        toValue: 1,
        duration: 1000,
        easing: Easing.linear,
        useNativeDriver: true,
      }).start(() => spin());
    };
    spin();
  }, [spinValue]);

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const sizes = {
    small: 20,
    medium: 32,
    large: 48,
  };

  const spinnerSize = sizes[size];
  const spinnerColor = color || theme.colors.primary;

  return (
    <Animated.View
      style={[
        styles.spinner,
        {
          width: spinnerSize,
          height: spinnerSize,
          borderColor: `${spinnerColor}20`,
          borderTopColor: spinnerColor,
          transform: [{ rotate: spin }],
        },
      ]}
    />
  );
};

interface LoadingOverlayProps {
  visible: boolean;
  message?: string;
  children?: React.ReactNode;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  visible,
  message = 'Loading...',
  children,
}) => {
  const { theme } = useTheme();

  if (!visible) return null;

  return (
    <View style={[styles.overlay, { backgroundColor: `${theme.colors.background}95` }]}>
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.surface }]}>
        <LoadingSpinner size="large" />
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>
          {message}
        </Text>
        {children}
      </View>
    </View>
  );
};

interface SkeletonProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: any;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius = BORDER_RADIUS.md,
  style,
}) => {
  const { theme } = useTheme();
  const animatedValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    const animate = () => {
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ]).start(() => animate());
    };
    animate();
  }, [animatedValue]);

  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.surfaceSecondary, theme.colors.surfaceTertiary],
  });

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          borderRadius,
          backgroundColor,
        },
        style,
      ]}
    />
  );
};

interface ProgressBarProps {
  progress: number; // 0 to 1
  height?: number;
  color?: string;
  backgroundColor?: string;
  animated?: boolean;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  height = 8,
  color,
  backgroundColor,
  animated = true,
}) => {
  const { theme } = useTheme();
  const animatedWidth = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (animated) {
      Animated.timing(animatedWidth, {
        toValue: progress,
        duration: 300,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: false,
      }).start();
    } else {
      animatedWidth.setValue(progress);
    }
  }, [progress, animated, animatedWidth]);

  const progressColor = color || theme.colors.primary;
  const bgColor = backgroundColor || theme.colors.surfaceSecondary;

  return (
    <View style={[styles.progressContainer, { height, backgroundColor: bgColor }]}>
      <Animated.View
        style={[
          styles.progressBar,
          {
            height,
            backgroundColor: progressColor,
            width: animatedWidth.interpolate({
              inputRange: [0, 1],
              outputRange: ['0%', '100%'],
              extrapolate: 'clamp',
            }),
          },
        ]}
      />
    </View>
  );
};

interface PulsingDotProps {
  color?: string;
  size?: number;
}

export const PulsingDot: React.FC<PulsingDotProps> = ({
  color,
  size = 8,
}) => {
  const { theme } = useTheme();
  const pulseValue = React.useRef(new Animated.Value(1)).current;

  React.useEffect(() => {
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseValue, {
          toValue: 0.3,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(pulseValue, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
      ]).start(() => pulse());
    };
    pulse();
  }, [pulseValue]);

  const dotColor = color || theme.colors.primary;

  return (
    <Animated.View
      style={[
        styles.pulsingDot,
        {
          width: size,
          height: size,
          backgroundColor: dotColor,
          opacity: pulseValue,
        },
      ]}
    />
  );
};

interface TypingIndicatorProps {
  visible: boolean;
  color?: string;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  visible,
  color,
}) => {
  const { theme } = useTheme();

  if (!visible) return null;

  return (
    <View style={styles.typingContainer}>
      <PulsingDot color={color} />
      <PulsingDot color={color} />
      <PulsingDot color={color} />
    </View>
  );
};

const styles = StyleSheet.create({
  spinner: {
    borderWidth: 3,
    borderRadius: 50,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingContainer: {
    padding: SPACING['3xl'],
    borderRadius: BORDER_RADIUS.xl,
    alignItems: 'center',
    minWidth: 200,
    shadowColor: COLORS.shadowDark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  loadingText: {
    marginTop: SPACING.lg,
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: '600',
    textAlign: 'center',
  },
  progressContainer: {
    borderRadius: BORDER_RADIUS.full,
    overflow: 'hidden',
  },
  progressBar: {
    borderRadius: BORDER_RADIUS.full,
  },
  pulsingDot: {
    borderRadius: 50,
    marginHorizontal: 2,
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
  },
});
