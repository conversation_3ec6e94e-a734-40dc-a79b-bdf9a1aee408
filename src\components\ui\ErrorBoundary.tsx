import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '@/constants';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface ErrorFallbackProps {
  error?: Error;
  resetError: () => void;
  retry?: () => void;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
    
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return (
        <FallbackComponent
          error={this.state.error}
          resetError={this.resetError}
        />
      );
    }

    return this.props.children;
  }
}

const DefaultErrorFallback: React.FC<ErrorFallbackProps> = ({ error, resetError, retry }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={[styles.errorCard, { backgroundColor: theme.colors.surface }]}>
        <View style={[styles.iconContainer, { backgroundColor: theme.colors.errorLight }]}>
          <Ionicons 
            name="alert-circle-outline" 
            size={48} 
            color={theme.colors.error} 
          />
        </View>
        
        <Text style={[styles.title, { color: theme.colors.text }]}>
          {t('error.somethingWentWrong')}
        </Text>
        
        <Text style={[styles.message, { color: theme.colors.textSecondary }]}>
          {t('error.unexpectedError')}
        </Text>
        
        {__DEV__ && error && (
          <View style={[styles.debugContainer, { backgroundColor: theme.colors.surfaceSecondary }]}>
            <Text style={[styles.debugTitle, { color: theme.colors.textSecondary }]}>
              Debug Info:
            </Text>
            <Text style={[styles.debugText, { color: theme.colors.textTertiary }]}>
              {error.message}
            </Text>
          </View>
        )}
        
        <View style={styles.buttonContainer}>
          {retry && (
            <TouchableOpacity
              style={[styles.button, styles.retryButton, { backgroundColor: theme.colors.primary }]}
              onPress={retry}
            >
              <Ionicons name="refresh" size={20} color={theme.colors.textOnPrimary} />
              <Text style={[styles.buttonText, { color: theme.colors.textOnPrimary }]}>
                {t('common.retry')}
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[styles.button, styles.resetButton, { borderColor: theme.colors.border }]}
            onPress={resetError}
          >
            <Ionicons name="home-outline" size={20} color={theme.colors.primary} />
            <Text style={[styles.buttonText, { color: theme.colors.primary }]}>
              {t('common.goHome')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

interface ErrorMessageProps {
  message: string;
  type?: 'error' | 'warning' | 'info';
  onRetry?: () => void;
  onDismiss?: () => void;
  visible?: boolean;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message,
  type = 'error',
  onRetry,
  onDismiss,
  visible = true,
}) => {
  const { theme } = useTheme();
  const { t } = useLanguage();

  if (!visible) return null;

  const getTypeConfig = () => {
    switch (type) {
      case 'warning':
        return {
          backgroundColor: theme.colors.warningLight,
          borderColor: theme.colors.warning,
          iconName: 'warning-outline' as const,
          iconColor: theme.colors.warning,
        };
      case 'info':
        return {
          backgroundColor: theme.colors.infoLight,
          borderColor: theme.colors.info,
          iconName: 'information-circle-outline' as const,
          iconColor: theme.colors.info,
        };
      default:
        return {
          backgroundColor: theme.colors.errorLight,
          borderColor: theme.colors.error,
          iconName: 'alert-circle-outline' as const,
          iconColor: theme.colors.error,
        };
    }
  };

  const typeConfig = getTypeConfig();

  return (
    <View style={[
      styles.errorMessage,
      {
        backgroundColor: typeConfig.backgroundColor,
        borderColor: typeConfig.borderColor,
      }
    ]}>
      <View style={styles.errorContent}>
        <Ionicons 
          name={typeConfig.iconName} 
          size={24} 
          color={typeConfig.iconColor} 
        />
        <Text style={[styles.errorText, { color: theme.colors.text }]}>
          {message}
        </Text>
      </View>
      
      <View style={styles.errorActions}>
        {onRetry && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: typeConfig.iconColor }]}
            onPress={onRetry}
          >
            <Ionicons name="refresh" size={16} color={theme.colors.textOnPrimary} />
            <Text style={[styles.actionButtonText, { color: theme.colors.textOnPrimary }]}>
              {t('common.retry')}
            </Text>
          </TouchableOpacity>
        )}
        
        {onDismiss && (
          <TouchableOpacity
            style={styles.dismissButton}
            onPress={onDismiss}
          >
            <Ionicons name="close" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

interface NetworkErrorProps {
  onRetry: () => void;
  visible: boolean;
}

export const NetworkError: React.FC<NetworkErrorProps> = ({ onRetry, visible }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();

  if (!visible) return null;

  return (
    <View style={[styles.networkError, { backgroundColor: theme.colors.surface }]}>
      <Ionicons name="wifi-outline" size={32} color={theme.colors.textSecondary} />
      <Text style={[styles.networkErrorTitle, { color: theme.colors.text }]}>
        {t('error.networkError')}
      </Text>
      <Text style={[styles.networkErrorMessage, { color: theme.colors.textSecondary }]}>
        {t('error.checkConnection')}
      </Text>
      <TouchableOpacity
        style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
        onPress={onRetry}
      >
        <Text style={[styles.buttonText, { color: theme.colors.textOnPrimary }]}>
          {t('common.retry')}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  errorCard: {
    padding: SPACING['3xl'],
    borderRadius: BORDER_RADIUS.xl,
    alignItems: 'center',
    maxWidth: 400,
    width: '100%',
    shadowColor: COLORS.shadowDark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  title: {
    fontSize: TYPOGRAPHY.fontSize['2xl'],
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  message: {
    fontSize: TYPOGRAPHY.fontSize.base,
    textAlign: 'center',
    marginBottom: SPACING.xl,
    lineHeight: TYPOGRAPHY.lineHeight.relaxed * TYPOGRAPHY.fontSize.base,
  },
  debugContainer: {
    width: '100%',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.xl,
  },
  debugTitle: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
    marginBottom: SPACING.sm,
  },
  debugText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontFamily: 'monospace',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    gap: SPACING.sm,
  },
  retryButton: {
    // backgroundColor set dynamically
  },
  resetButton: {
    borderWidth: 1,
  },
  buttonText: {
    fontSize: TYPOGRAPHY.fontSize.base,
    fontWeight: '600',
  },
  errorMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    margin: SPACING.md,
  },
  errorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: SPACING.md,
  },
  errorText: {
    fontSize: TYPOGRAPHY.fontSize.base,
    flex: 1,
  },
  errorActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    gap: SPACING.xs,
  },
  actionButtonText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
  },
  dismissButton: {
    padding: SPACING.sm,
  },
  networkError: {
    alignItems: 'center',
    padding: SPACING['3xl'],
    borderRadius: BORDER_RADIUS.xl,
    margin: SPACING.xl,
  },
  networkErrorTitle: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: 'bold',
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  networkErrorMessage: {
    fontSize: TYPOGRAPHY.fontSize.base,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
});
