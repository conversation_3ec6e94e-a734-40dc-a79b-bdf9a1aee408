import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import { Icon, ICON_SIZES } from '@/components/ui/Icon';
import {
  FadeInView,
  SlideUpView,
  ScaleInView,
  StaggeredList,
  BounceView
} from '@/components/animations/AnimatedComponents';
import { HomeScreenSkeleton } from '@/components/loading/SkeletonScreens';
import { useTheme, useThemedStyles } from '@/contexts/ThemeContext';
import { FileService } from '@/services/fileProcessing/fileService';
import { DatabaseService } from '@/services/storage/database';
import { Theme } from '@/constants/themes';
import { QuizHistoryItem } from '@/types';

interface QuizStats {
  totalQuizzes: number;
  averageScore: number;
  totalQuestions: number;
  studyStreak: number;
  lastQuizDate?: string;
}



interface HomeScreenProps {
  navigation: any;
}

export const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = useThemedStyles(createStyles);

  const [recentQuizzes, setRecentQuizzes] = useState<QuizHistoryItem[]>([]);
  const [stats, setStats] = useState<QuizStats>({
    totalQuizzes: 0,
    averageScore: 0,
    totalQuestions: 0,
    studyStreak: 0,
  });
  const [isLoading, setIsLoading] = useState(true);


  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      await Promise.all([
        loadRecentQuizzes(),
        loadStats(),
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const loadRecentQuizzes = async () => {
    try {
      const history = await DatabaseService.getQuizHistory();
      setRecentQuizzes(history.slice(0, 3)); // Show only 3 most recent
    } catch (error) {
      console.error('Error loading recent quizzes:', error);
    }
  };

  const loadStats = async () => {
    try {
      const history = await DatabaseService.getQuizHistory();
      const totalQuizzes = history.length;
      const totalQuestions = history.reduce((sum, quiz) => sum + quiz.totalQuestions, 0);
      const averageScore = totalQuizzes > 0
        ? history.reduce((sum, quiz) => sum + quiz.score, 0) / totalQuizzes
        : 0;

      // Calculate study streak
      const studyStreak = await calculateStudyStreak(history);

      setStats({
        totalQuizzes,
        averageScore: Math.round(averageScore),
        totalQuestions,
        studyStreak,
        lastQuizDate: history[0]?.date.toISOString(),
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const calculateStudyStreak = async (history: QuizHistoryItem[]): Promise<number> => {
    if (history.length === 0) return 0;

    // Sort by date (most recent first)
    const sortedHistory = history.sort((a, b) =>
      new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    let streak = 0;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (const quiz of sortedHistory) {
      const quizDate = new Date(quiz.date);
      quizDate.setHours(0, 0, 0, 0);

      const daysDiff = Math.floor((today.getTime() - quizDate.getTime()) / (1000 * 60 * 60 * 24));

      if (daysDiff === streak) {
        streak++;
      } else if (daysDiff > streak) {
        break;
      }
    }

    return streak;
  };



  const handleUploadFile = async () => {
    try {
      const file = await FileService.pickFile();

      if (file) {
        // Navigate to quiz setup with the selected file
        navigation.navigate('QuizSetup', { file });
      }
    } catch (error) {
      console.error('Error picking file:', error);
      Alert.alert(
        t('common.error'),
        error instanceof Error ? error.message : t('errors.fileUpload')
      );
    }
  };

  const handleStartQuiz = (quizId: string) => {
    navigation.navigate('Quiz', { quizId });
  };

  if (isLoading) {
    return <HomeScreenSkeleton />;
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      {/* Header */}
      <FadeInView style={styles.header}>
        <Text style={styles.title}>{t('home.welcome')}</Text>
        <Text style={styles.subtitle}>{t('home.subtitle')}</Text>
      </FadeInView>

      {/* Stats Cards */}
      <StaggeredList
        style={styles.statsContainer}
        staggerDelay={150}
        animationType="scaleIn"
      >
        <View style={styles.statsRow}>
          <View style={[styles.statCard, { backgroundColor: COLORS.primary }]}>
            <Text style={styles.statNumber}>{stats.totalQuizzes}</Text>
            <Text style={styles.statLabel}>Total Quizzes</Text>
          </View>
          <View style={[styles.statCard, { backgroundColor: COLORS.secondary }]}>
            <Text style={styles.statNumber}>{stats.averageScore}%</Text>
            <Text style={styles.statLabel}>Avg Score</Text>
          </View>
        </View>
        <View style={styles.statsRow}>
          <View style={[styles.statCard, { backgroundColor: COLORS.accent }]}>
            <Text style={styles.statNumber}>{stats.studyStreak}</Text>
            <Text style={styles.statLabel}>Study Streak</Text>
          </View>
          <View style={[styles.statCard, { backgroundColor: COLORS.info }]}>
            <Text style={styles.statNumber}>{stats.totalQuestions}</Text>
            <Text style={styles.statLabel}>Questions</Text>
          </View>
        </View>
      </StaggeredList>

      {/* Primary Action Section */}
      <SlideUpView delay={300} style={styles.primaryActionSection}>
        <BounceView onPress={handleUploadFile}>
          <LinearGradient
            colors={[COLORS.primary, COLORS.primaryDark]}
            style={styles.primaryActionCard}
          >
            <View style={styles.primaryActionContent}>
              <Icon
                name="cloud-upload-outline"
                library="MaterialCommunityIcons"
                size={ICON_SIZES['2xl']}
                color={COLORS.textOnPrimary}
              />
              <Text style={styles.primaryActionTitle}>{t('home.uploadFile')}</Text>
              <Text style={styles.primaryActionSubtitle}>
                Upload PDF, images, or documents to create personalized quizzes
              </Text>
              <TouchableOpacity
                style={styles.primaryActionButton}
                onPress={handleUploadFile}
              >
                <Text style={styles.primaryActionButtonText}>Get Started</Text>
              </TouchableOpacity>
            </View>
          </LinearGradient>
        </BounceView>
      </SlideUpView>

      {/* Navigation Grid */}
      <SlideUpView delay={500} style={styles.navigationSection}>
        <Text style={styles.sectionTitle}>Explore</Text>
        <View style={styles.navigationGrid}>
          <BounceView onPress={() => navigation.navigate('History')}>
            <View style={styles.navigationCard}>
              <LinearGradient
                colors={[COLORS.cardBackground, COLORS.surfaceSecondary]}
                style={styles.navigationCardGradient}
              >
                <Icon
                  name="chart-line"
                  library="MaterialCommunityIcons"
                  size={ICON_SIZES.xl}
                  color={COLORS.secondary}
                />
                <Text style={styles.navigationCardTitle}>Progress</Text>
                <Text style={styles.navigationCardSubtitle}>{stats.totalQuizzes} quizzes</Text>
              </LinearGradient>
            </View>
          </BounceView>

          <BounceView onPress={() => navigation.navigate('Chat')}>
            <View style={styles.navigationCard}>
              <LinearGradient
                colors={[COLORS.cardBackground, COLORS.surfaceSecondary]}
                style={styles.navigationCardGradient}
              >
                <Icon
                  name="robot-outline"
                  library="MaterialCommunityIcons"
                  size={ICON_SIZES.xl}
                  color={COLORS.accent}
                />
                <Text style={styles.navigationCardTitle}>AI Tutor</Text>
                <Text style={styles.navigationCardSubtitle}>Study assistant</Text>
              </LinearGradient>
            </View>
          </BounceView>
        </View>
      </SlideUpView>

      {/* Recent Quizzes */}
      <View style={styles.recentSection}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>{t('home.recentQuizzes')}</Text>
          {recentQuizzes.length > 0 && (
            <TouchableOpacity onPress={() => navigation.navigate('History')}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          )}
        </View>

        {recentQuizzes.length === 0 ? (
          <View style={styles.emptyState}>
            <Icon
              name="book-outline"
              library="MaterialCommunityIcons"
              size={ICON_SIZES['3xl']}
              color={COLORS.primary}
              style={styles.emptyIcon}
            />
            <Text style={styles.emptyText}>{t('home.noRecentQuizzes')}</Text>
            <Text style={styles.emptySubtext}>Upload a file to create your first quiz</Text>
          </View>
        ) : (
          <View style={styles.quizList}>
            {recentQuizzes.map((quiz) => (
              <TouchableOpacity
                key={quiz.id}
                style={styles.quizItem}
                onPress={() => handleStartQuiz(quiz.id)}
              >
                <View style={styles.quizInfo}>
                  <Text style={styles.quizSubject}>{quiz.subject}</Text>
                  <Text style={styles.quizDate}>
                    {quiz.date.toLocaleDateString()}
                  </Text>
                  <View style={styles.quizScoreContainer}>
                    <Text style={styles.quizScore}>
                      {quiz.score}%
                    </Text>
                    <Text style={styles.quizQuestions}>
                      {quiz.totalQuestions} questions
                    </Text>
                  </View>
                </View>
                <View style={styles.quizActions}>
                  <Text style={styles.quizActionText}>Tap to review</Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>

      {/* Featured Content */}
      <View style={styles.featuredSection}>
        <Text style={styles.sectionTitle}>Study Tips</Text>
        <View style={styles.tipCard}>
          <View style={styles.tipIconContainer}>
            <Icon
              name="lightbulb-outline"
              library="MaterialCommunityIcons"
              size={ICON_SIZES.lg}
              color={COLORS.primary}
            />
          </View>
          <View style={styles.tipContent}>
            <Text style={styles.tipTitle}>Daily Study Habit</Text>
            <Text style={styles.tipText}>
              Consistent daily practice, even for 15 minutes, is more effective than long cramming sessions.
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    padding: 20,
  },
  header: {
    marginBottom: 30,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  uploadSection: {
    marginBottom: 40,
  },
  uploadButton: {
    marginHorizontal: 20,
  },
  recentSection: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginBottom: 16,
  },
  emptyState: {
    padding: 40,
    alignItems: 'center',
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.borderLight,
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  quizList: {
    gap: 12,
  },
  quizItem: {
    backgroundColor: COLORS.cardBackground,
    padding: 16,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: COLORS.borderLight,
    shadowColor: COLORS.primaryDark,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  quizInfo: {
    flex: 1,
  },
  quizSubject: {
    fontSize: 16,
    fontFamily: FONTS.medium,
    color: COLORS.text,
    marginBottom: 4,
  },
  quizDate: {
    fontSize: 14,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    marginBottom: 2,
  },
  quizScore: {
    fontSize: 18,
    fontFamily: FONTS.bold,
    color: COLORS.primary,
  },
  quizQuestions: {
    fontSize: 14,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
  },
  quizScoreContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  quizActions: {
    alignItems: 'center',
  },
  quizActionText: {
    fontSize: 12,
    fontFamily: FONTS.regular,
    color: COLORS.primary,
    fontStyle: 'italic',
  },
  // Stats Cards
  statsContainer: {
    marginBottom: 24,
  },
  statsRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statNumber: {
    fontSize: 24,
    fontFamily: FONTS.bold,
    color: COLORS.surface,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: FONTS.regular,
    color: COLORS.surface,
    opacity: 0.9,
  },

  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    fontFamily: FONTS.medium,
    color: COLORS.primary,
  },
  emptyIcon: {
    marginBottom: SPACING.lg,
  },
  emptySubtext: {
    fontSize: 14,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  // Featured Content
  featuredSection: {
    marginBottom: 20,
  },
  tipCard: {
    backgroundColor: COLORS.surfaceTertiary,
    padding: 16,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderWidth: 1,
    borderColor: COLORS.borderLight,
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  tipIconContainer: {
    marginRight: SPACING.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 16,
    fontFamily: FONTS.medium,
    color: COLORS.text,
    marginBottom: 4,
  },
  tipText: {
    fontSize: 14,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    lineHeight: 20,
  },
  // Primary Action Section
  primaryActionSection: {
    marginBottom: SPACING['3xl'],
  },
  primaryActionCard: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING['3xl'],
    alignItems: 'center',
    ...SHADOWS.lg,
  },
  primaryActionContent: {
    alignItems: 'center',
  },
  primaryActionTitle: {
    fontSize: TYPOGRAPHY.fontSize['2xl'],
    fontFamily: FONTS.bold,
    color: COLORS.textOnPrimary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  primaryActionSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.base,
    fontFamily: FONTS.regular,
    color: COLORS.textOnPrimary,
    textAlign: 'center',
    opacity: 0.9,
    marginBottom: SPACING.xl,
    lineHeight: TYPOGRAPHY.lineHeight.relaxed * TYPOGRAPHY.fontSize.base,
  },
  primaryActionButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: SPACING['2xl'],
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  primaryActionButtonText: {
    fontSize: TYPOGRAPHY.fontSize.base,
    fontFamily: FONTS.semiBold,
    color: COLORS.textOnPrimary,
  },
  // Navigation Section
  navigationSection: {
    marginBottom: SPACING['3xl'],
  },
  navigationGrid: {
    flexDirection: 'row',
    gap: SPACING.lg,
  },
  navigationCard: {
    flex: 1,
    borderRadius: BORDER_RADIUS.xl,
    overflow: 'hidden',
    ...SHADOWS.md,
  },
  navigationCardGradient: {
    padding: SPACING.xl,
    alignItems: 'center',
    minHeight: 120,
    justifyContent: 'center',
  },
  navigationCardTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontFamily: FONTS.semiBold,
    color: COLORS.text,
    marginTop: SPACING.md,
    marginBottom: SPACING.xs,
    textAlign: 'center',
  },
  navigationCardSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
});
