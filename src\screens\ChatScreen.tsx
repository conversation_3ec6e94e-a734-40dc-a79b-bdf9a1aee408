import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  Alert,
  Share,
  Animated,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import { Icon, ICON_SIZES } from '@/components/ui/Icon';
import { COLORS, FONTS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS } from '@/constants';
import { ChatService, ChatMessage } from '@/services/api/chatService';
import { LoadingSpinner, TypingIndicator } from '@/components/ui/LoadingStates';
import { ErrorBoundary, ErrorMessage } from '@/components/ui/ErrorBoundary';
import { useTheme } from '@/contexts/ThemeContext';

interface ChatScreenProps {
  navigation?: any;
}

export const ChatScreen: React.FC<ChatScreenProps> = ({ navigation }) => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(true);
  const [isInitializing, setIsInitializing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const flatListRef = useRef<FlatList>(null);
  const typingAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    initializeChat();
    startFadeInAnimation();
  }, []);

  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages]);

  const initializeChat = async () => {
    try {
      // Load conversation history
      const history = ChatService.getConversationHistory();
      setMessages(history);

      // Set up context
      ChatService.setContext({
        userPreferences: {
          language: i18n.language as 'en' | 'ar',
          responseStyle: 'educational',
        },
      });

      // Add welcome message if no history
      if (history.length === 0) {
        const welcomeMessage = ChatService.addMessage({
          text: t('chat.welcomeMessage') || 'Hello! I\'m your AI assistant. How can I help you with your studies today?',
          isUser: false,
        });
        setMessages([welcomeMessage]);
      }

      setShowQuickActions(history.length === 0);
    } catch (error) {
      console.error('Error initializing chat:', error);
      setError('Failed to initialize chat');
    } finally {
      setIsInitializing(false);
    }
  };

  const startFadeInAnimation = () => {
    Animated.timing(fadeAnimation, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  const startTypingAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(typingAnimation, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(typingAnimation, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const stopTypingAnimation = () => {
    typingAnimation.stopAnimation();
    typingAnimation.setValue(0);
  };

  const scrollToBottom = () => {
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  const sendMessage = async (text: string) => {
    if (!text.trim()) return;

    try {
      setIsLoading(true);
      setShowQuickActions(false);
      setError(null);
      startTypingAnimation();

      // Send message and get response
      const aiResponse = await ChatService.sendMessage(text);

      // Update messages with the latest conversation history
      const updatedHistory = ChatService.getConversationHistory();
      setMessages([...updatedHistory]);

      setInputText('');
    } catch (error: any) {
      console.error('Error sending message:', error);
      setError(error.message || 'Failed to send message');

      Alert.alert(
        t('common.error') || 'Error',
        error.message || 'Failed to send message. Please check your API key in Settings.',
        [
          { text: t('common.cancel') || 'Cancel', style: 'cancel' },
          {
            text: 'Settings',
            onPress: () => navigation?.navigate('Settings')
          },
          {
            text: t('common.retry') || 'Retry',
            onPress: () => sendMessage(text)
          },
        ]
      );
    } finally {
      setIsLoading(false);
      stopTypingAnimation();
    }
  };

  const handleQuickAction = (prompt: string) => {
    sendMessage(prompt);
  };

  const exportChat = async () => {
    try {
      const messages = ChatService.getConversationHistory();
      const chatData = messages.map(msg =>
        `${msg.isUser ? 'User' : 'AI'}: ${msg.text}`
      ).join('\n\n');

      await Share.share({
        message: chatData,
        title: 'Chat Export',
      });
    } catch (error) {
      Alert.alert(t('common.error'), 'Failed to export chat');
    }
  };

  const clearChat = () => {
    Alert.alert(
      'Clear Chat',
      'Are you sure you want to clear this conversation?',
      [
        { text: t('common.cancel') || 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: () => {
            ChatService.clearHistory();
            setMessages([]);
            setShowQuickActions(true);
            // Add welcome message
            const welcomeMessage = ChatService.addMessage({
              text: t('chat.welcomeMessage') || 'Hello! How can I help you with your studies today?',
              isUser: false,
            });
            setMessages([welcomeMessage]);
          },
        },
      ]
    );
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => {
    if (item.isTyping) {
      return (
        <View style={styles.messageContainer}>
          <View style={[styles.messageBubble, styles.aiMessageBubble]}>
            <Animated.View style={[styles.typingIndicator, { opacity: typingAnimation }]}>
              <Text style={styles.typingText}>AI is thinking</Text>
              <View style={styles.typingDots}>
                <View style={styles.typingDot} />
                <View style={styles.typingDot} />
                <View style={styles.typingDot} />
              </View>
            </Animated.View>
          </View>
        </View>
      );
    }

    return (
      <Animated.View
        style={[
          styles.messageContainer,
          item.isUser ? styles.userMessageContainer : styles.aiMessageContainer,
          { opacity: fadeAnimation }
        ]}
      >
        <LinearGradient
          colors={
            item.isUser
              ? [COLORS.primary, COLORS.primaryDark]
              : item.error
              ? [COLORS.error, '#dc2626']
              : [COLORS.cardBackground, COLORS.surfaceSecondary]
          }
          style={[
            styles.messageBubble,
            item.isUser ? styles.userMessageBubble : styles.aiMessageBubble,
          ]}
        >
          {item.isTyping ? (
            <TypingIndicator visible={true} color={COLORS.textSecondary} />
          ) : (
            <Text
              style={[
                styles.messageText,
                item.isUser ? styles.userMessageText : styles.aiMessageText,
                item.error && styles.errorMessageText,
              ]}
            >
              {item.text}
            </Text>
          )}
          <Text
            style={[
              styles.messageTime,
              item.isUser ? styles.userMessageTime : styles.aiMessageTime,
            ]}
          >
            {new Date(item.timestamp).toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </Text>
        </LinearGradient>
      </Animated.View>
    );
  };

  const renderQuickActions = () => {
    if (!showQuickActions) return null;

    const quickActions = [
      {
        id: 'explain',
        title: 'Explain this concept',
        titleAr: 'اشرح هذا المفهوم',
        prompt: 'Can you explain this concept in detail?',
        iconName: 'help-circle-outline',
      },
      {
        id: 'hint',
        title: 'Give me a hint',
        titleAr: 'أعطني تلميحاً',
        prompt: 'Can you give me a hint without revealing the answer?',
        iconName: 'bulb-outline',
      },
      {
        id: 'similar',
        title: 'Similar questions',
        titleAr: 'أسئلة مشابهة',
        prompt: 'Can you suggest similar questions or topics I should study?',
        iconName: 'list-outline',
      },
      {
        id: 'study',
        title: 'Study tips',
        titleAr: 'نصائح للدراسة',
        prompt: 'Can you give me study tips for this topic?',
        iconName: 'school-outline',
      },
    ];

    return (
      <Animated.View style={[styles.quickActionsContainer, { opacity: fadeAnimation }]}>
        <Text style={styles.quickActionsTitle}>
          {t('chat.quickActions') || 'Quick Actions'}
        </Text>
        <View style={styles.quickActionsGrid}>
          {quickActions.map((action) => (
            <TouchableOpacity
              key={action.id}
              style={styles.quickActionButton}
              onPress={() => handleQuickAction(action.prompt)}
            >
              <LinearGradient
                colors={[COLORS.surfaceTertiary, COLORS.surfaceSecondary]}
                style={styles.quickActionGradient}
              >
                <Icon
                  name={action.iconName}
                  size={ICON_SIZES.lg}
                  color={COLORS.primary}
                  style={styles.quickActionIcon}
                />
                <Text style={styles.quickActionText}>
                  {i18n.language === 'ar' ? action.titleAr : action.title}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          ))}
        </View>
      </Animated.View>
    );
  };

  const renderHeader = () => (
    <LinearGradient
      colors={[COLORS.primaryDeep, COLORS.primary]}
      style={styles.header}
    >
      <Text style={styles.headerTitle}>{t('chat.title')}</Text>
      <View style={styles.headerActions}>
        <TouchableOpacity onPress={exportChat} style={styles.headerButton}>
          <Icon
            name="share-variant"
            library="MaterialCommunityIcons"
            size={ICON_SIZES.md}
            color={COLORS.textOnPrimary}
          />
        </TouchableOpacity>
        <TouchableOpacity onPress={clearChat} style={styles.headerButton}>
          <Icon
            name="delete-outline"
            library="MaterialCommunityIcons"
            size={ICON_SIZES.md}
            color={COLORS.textOnPrimary}
          />
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );

  const renderInputSection = () => (
    <LinearGradient
      colors={[COLORS.cardBackground, COLORS.surfaceSecondary]}
      style={styles.inputContainer}
    >
      <View style={styles.inputRow}>
        <TextInput
          style={[
            styles.textInput,
            i18n.language === 'ar' && styles.rtlTextInput
          ]}
          value={inputText}
          onChangeText={setInputText}
          placeholder={t('chat.placeholder')}
          placeholderTextColor={COLORS.textSecondary}
          multiline
          maxLength={1000}
          editable={!isLoading}
          textAlign={i18n.language === 'ar' ? 'right' : 'left'}
        />
        <TouchableOpacity
          style={[
            styles.sendButton,
            (!inputText.trim() || isLoading) && styles.sendButtonDisabled
          ]}
          onPress={() => sendMessage(inputText)}
          disabled={!inputText.trim() || isLoading}
        >
          <LinearGradient
            colors={
              !inputText.trim() || isLoading
                ? [COLORS.textSecondary, COLORS.textSecondary]
                : [COLORS.primary, COLORS.primaryDark]
            }
            style={styles.sendButtonGradient}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color={COLORS.textOnPrimary} />
            ) : (
              <Text style={styles.sendButtonText}>
                {i18n.language === 'ar' ? '←' : '→'}
              </Text>
            )}
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );

  if (isInitializing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
        <Text style={styles.loadingText}>Initializing chat...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <LinearGradient
        colors={[COLORS.background, COLORS.surfaceSecondary]}
        style={styles.backgroundGradient}
      >
        {renderHeader()}

        <View style={styles.chatContainer}>
          {messages.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Icon
                name="robot-outline"
                library="MaterialCommunityIcons"
                size={ICON_SIZES['3xl']}
                color={COLORS.primary}
                style={styles.emptyIcon}
              />
              <Text style={styles.emptyTitle}>
                {t('chat.welcomeTitle')}
              </Text>
              <Text style={styles.emptySubtitle}>
                {t('chat.welcomeMessage')}
              </Text>
              {renderQuickActions()}
            </View>
          ) : (
            <FlatList
              ref={flatListRef}
              data={messages}
              renderItem={renderMessage}
              keyExtractor={(item) => item.id}
              style={styles.messagesList}
              contentContainerStyle={styles.messagesContent}
              showsVerticalScrollIndicator={false}
              onContentSizeChange={scrollToBottom}
            />
          )}
        </View>

        {renderInputSection()}
      </LinearGradient>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
  },
  // Header Styles
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    shadowColor: COLORS.primaryVeryDark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: FONTS.bold,
    color: COLORS.textOnPrimary,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  headerButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerButtonText: {
    fontSize: 16,
  },
  // Chat Container
  chatContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyIcon: {
    marginBottom: SPACING.lg,
  },
  emptyTitle: {
    fontSize: 24,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  // Messages
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: 16,
    paddingBottom: 32,
  },
  messageContainer: {
    marginVertical: 4,
    paddingHorizontal: 4,
  },
  userMessageContainer: {
    alignItems: 'flex-end',
  },
  aiMessageContainer: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  userMessageBubble: {
    borderBottomRightRadius: 4,
  },
  aiMessageBubble: {
    borderBottomLeftRadius: 4,
    borderWidth: 1,
    borderColor: COLORS.borderLight,
  },
  messageText: {
    fontSize: 16,
    fontFamily: FONTS.regular,
    lineHeight: 22,
    marginBottom: 4,
  },
  userMessageText: {
    color: COLORS.textOnPrimary,
  },
  aiMessageText: {
    color: COLORS.textOnSurface,
  },
  errorMessageText: {
    color: COLORS.textOnPrimary,
  },
  messageTime: {
    fontSize: 12,
    fontFamily: FONTS.regular,
    opacity: 0.7,
  },
  userMessageTime: {
    color: COLORS.textOnPrimary,
    textAlign: 'right',
  },
  aiMessageTime: {
    color: COLORS.textSecondary,
    textAlign: 'left',
  },
  // Typing Indicator
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  typingText: {
    fontSize: 14,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
  },
  typingDots: {
    flexDirection: 'row',
    gap: 4,
  },
  typingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: COLORS.primary,
  },
  // Quick Actions
  quickActionsContainer: {
    marginTop: 20,
    width: '100%',
  },
  quickActionsTitle: {
    fontSize: 18,
    fontFamily: FONTS.medium,
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 12,
  },
  quickActionButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionGradient: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
    minWidth: 120,
    borderWidth: 1,
    borderColor: COLORS.borderLight,
  },
  quickActionIcon: {
    marginBottom: SPACING.xs,
  },
  quickActionText: {
    fontSize: 12,
    fontFamily: FONTS.medium,
    color: COLORS.text,
    textAlign: 'center',
  },
  // Input Section
  inputContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingBottom: Platform.OS === 'ios' ? 34 : 12,
    borderTopWidth: 1,
    borderTopColor: COLORS.borderLight,
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: COLORS.borderLight,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: FONTS.regular,
    color: COLORS.textOnSurface,
    backgroundColor: COLORS.inputBackground,
    maxHeight: 100,
    textAlignVertical: 'center',
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  rtlTextInput: {
    textAlign: 'right',
    writingDirection: 'rtl',
  },
  sendButton: {
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  sendButtonGradient: {
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonText: {
    fontSize: 20,
    fontFamily: FONTS.bold,
    color: COLORS.textOnPrimary,
  },
});
