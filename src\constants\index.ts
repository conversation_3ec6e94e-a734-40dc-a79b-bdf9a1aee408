export const API_CONFIG = {
  GEMINI_API_KEY: process.env.EXPO_PUBLIC_GEMINI_API_KEY || '',
  GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent',
  GEMINI_VISION_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent',
  REQUEST_TIMEOUT: 60000, // 60 seconds
  MAX_RETRIES: 3,
  RETRY_DELAY: 2000, // 2 seconds
};

export const STORAGE_KEYS = {
  USER_SETTINGS: '@user_settings',
  QUIZ_HISTORY: '@quiz_history',
  CURRENT_QUIZ: '@current_quiz',
  API_KEYS: '@api_keys',
  CHAT_HISTORY: '@chat_history',
  USER_PREFERENCES: '@user_preferences',
};

export const QUIZ_CONFIG = {
  MIN_QUESTIONS: 5,
  MAX_QUESTIONS: 50,
  DEFAULT_QUESTIONS: 15,
  TIME_PRESETS: [15, 30, 45, 60], // minutes
  DEFAULT_TIME_LIMIT: 30, // minutes
};

export const SUPPORTED_FILE_TYPES = {
  PDF: ['application/pdf'],
  IMAGE: ['image/jpeg', 'image/jpg', 'image/png'],
};

export const MATERIAL_SCOPES = [
  { value: 'single_chapter', label: { en: 'Single Chapter/Unit', ar: 'فصل/وحدة واحدة' } },
  { value: 'full_semester', label: { en: 'Full Semester', ar: 'فصل دراسي كامل' } },
  { value: 'book', label: { en: 'Book', ar: 'كتاب' } },
  { value: 'exam_paper', label: { en: 'Exam Paper', ar: 'ورقة امتحان' } },
];

export const QUESTION_TYPES = [
  { value: 'multiple_choice', label: { en: 'Multiple Choice', ar: 'اختيار من متعدد' } },
  { value: 'true_false', label: { en: 'True/False', ar: 'صح / خطأ' } },
  { value: 'explain_justify', label: { en: 'Explain/Justify', ar: 'علل / اشرح' } },
  { value: 'mix', label: { en: 'Mix (All Types)', ar: 'مزيج من كل الأنواع' } },
];

export const COLORS = {
  // Primary Purple Palette - Professional Grade
  primary: '#7c3aed',        // Purple-600 - Main brand color
  primaryLight: '#a855f7',   // Purple-500 - Lighter variant
  primaryDark: '#6d28d9',    // Purple-700 - Darker variant
  primaryDeep: '#5b21b6',    // Purple-800 - Deep purple for headers
  primaryVeryDark: '#4c1d95', // Purple-900 - Very dark purple
  primaryUltraDark: '#3730a3', // Purple-950 - Ultra dark for premium feel

  // Secondary Purple Variants - Enterprise Level
  secondary: '#c084fc',      // Purple-400 - Secondary actions
  secondaryLight: '#d8b4fe', // Purple-300 - Light secondary
  secondaryDark: '#a855f7',  // Purple-500 - Dark secondary
  accent: '#e879f9',         // Fuchsia-400 - Accent highlights
  accentLight: '#f0abfc',    // Fuchsia-300 - Light accent

  // Professional Background System (No pure white)
  background: '#faf5ff',     // Purple-50 - Main app background
  backgroundSecondary: '#f3e8ff', // Purple-100 - Secondary background
  surface: '#f8f4ff',        // Custom light purple - Card backgrounds
  surfaceElevated: '#f5f3ff', // Elevated surface with subtle purple
  surfaceSecondary: '#f3e8ff', // Purple-100 - Secondary surfaces
  surfaceTertiary: '#ede9fe', // Purple-200 - Tertiary surfaces

  // Professional Container System
  modalBackground: '#f8f4ff', // Light purple for modals
  cardBackground: '#f8f4ff',  // Light purple for cards
  cardElevated: '#f5f3ff',   // Elevated cards
  inputBackground: '#f3e8ff', // Purple-100 for inputs
  headerBackground: '#5b21b6', // Professional header background

  // Status Colors - Professional System
  success: '#059669',        // Emerald-600 - Professional success
  successLight: '#d1fae5',   // Emerald-100 - Light success background
  error: '#dc2626',          // Red-600 - Professional error
  errorLight: '#fee2e2',     // Red-100 - Light error background
  warning: '#d97706',        // Amber-600 - Professional warning
  warningLight: '#fef3c7',   // Amber-100 - Light warning background
  info: '#8b5cf6',          // Purple-500 for info
  infoLight: '#ede9fe',     // Purple-200 - Light info background

  // Professional Text System
  text: '#111827',          // Gray-900 - Primary text (darker for better contrast)
  textSecondary: '#4b5563',  // Gray-600 - Secondary text
  textTertiary: '#6b7280',   // Gray-500 - Tertiary text
  textOnPrimary: '#ffffff',  // White text on purple backgrounds
  textOnSurface: '#374151', // Gray-700 - Text on light purple surfaces
  textMuted: '#9ca3af',     // Gray-400 - Muted text

  // Professional Border System
  border: '#e5e7eb',        // Gray-200 - Light borders
  borderSecondary: '#d1d5db', // Gray-300 - Secondary borders
  borderPrimary: '#c084fc', // Purple-400 borders
  borderLight: '#ddd6fe',   // Purple-300 - Light purple borders
  borderDark: '#6b7280',    // Gray-500 - Dark borders

  // Advanced Gradient System
  gradientPrimary: ['#7c3aed', '#6d28d9'], // Primary gradient
  gradientSecondary: ['#a855f7', '#8b5cf6'], // Secondary gradient
  gradientAccent: ['#e879f9', '#d946ef'], // Accent gradient
  gradientSurface: ['#f8f4ff', '#f3e8ff'], // Surface gradient
  gradientHeader: ['#5b21b6', '#4c1d95'], // Header gradient

  // Professional Shadow System
  shadowLight: 'rgba(124, 58, 237, 0.1)', // Light purple shadow
  shadowMedium: 'rgba(124, 58, 237, 0.15)', // Medium purple shadow
  shadowDark: 'rgba(124, 58, 237, 0.2)', // Dark purple shadow
  shadowBlack: 'rgba(0, 0, 0, 0.1)', // Traditional black shadow for depth
};

// Professional Typography System
export const TYPOGRAPHY = {
  // Font Families
  fontFamily: {
    regular: 'System',
    medium: 'System',
    semiBold: 'System',
    bold: 'System',
    arabic: 'System', // Will be replaced with a proper Arabic font
  },

  // Font Sizes - Professional Scale
  fontSize: {
    xs: 12,    // Extra small text
    sm: 14,    // Small text
    base: 16,  // Base text size
    lg: 18,    // Large text
    xl: 20,    // Extra large text
    '2xl': 24, // 2x large text
    '3xl': 30, // 3x large text
    '4xl': 36, // 4x large text
    '5xl': 48, // 5x large text
  },

  // Line Heights - Professional Spacing
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },

  // Letter Spacing
  letterSpacing: {
    tight: -0.5,
    normal: 0,
    wide: 0.5,
  },
};

// Legacy FONTS export for backward compatibility
export const FONTS = TYPOGRAPHY.fontFamily;

// Professional Spacing System
export const SPACING = {
  xs: 4,    // Extra small spacing
  sm: 8,    // Small spacing
  md: 12,   // Medium spacing
  lg: 16,   // Large spacing
  xl: 20,   // Extra large spacing
  '2xl': 24, // 2x large spacing
  '3xl': 32, // 3x large spacing
  '4xl': 40, // 4x large spacing
  '5xl': 48, // 5x large spacing
  '6xl': 64, // 6x large spacing
};

// Professional Border Radius System
export const BORDER_RADIUS = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 20,
  '3xl': 24,
  full: 9999,
};

// Professional Shadow System
export const SHADOWS = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: COLORS.shadowLight,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: COLORS.shadowMedium,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: COLORS.shadowDark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 1,
    shadowRadius: 8,
    elevation: 8,
  },
  xl: {
    shadowColor: COLORS.shadowDark,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 1,
    shadowRadius: 16,
    elevation: 16,
  },
};

export const API_MODELS = {
  GEMINI_PRO: 'gemini-1.5-pro',
  GEMINI_FLASH: 'gemini-1.5-flash',
  GEMINI_2_FLASH: 'gemini-2.0-flash-exp',
  GEMINI_PRO_VISION: 'gemini-1.5-pro-vision',
};

export const NOTIFICATION_TYPES = {
  STUDY_REMINDER: 'study_reminder',
  QUIZ_COMPLETION: 'quiz_completion',
  STREAK_MILESTONE: 'streak_milestone',
};

export const CHAT_CONFIG = {
  MAX_MESSAGES: 100,
  TYPING_DELAY: 1000,
  MAX_MESSAGE_LENGTH: 1000,
};

export const ANALYTICS_CONFIG = {
  CHART_COLORS: [COLORS.primary, COLORS.secondary, COLORS.accent, COLORS.info],
  DATE_RANGES: ['7d', '30d', '90d', '1y'],
};
