import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TextInput,
  Switch,
  TouchableOpacity,
  Modal,
  Animated,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import { Button } from '@/components/ui/Button';
import { Icon, ICON_SIZES } from '@/components/ui/Icon';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import { useTheme, useThemedStyles } from '@/contexts/ThemeContext';
import { AsyncStorageService } from '@/services/storage/asyncStorage';
import { DatabaseService } from '@/services/storage/database';
import { ApiKeyService, ApiKey } from '@/services/api/apiKeyService';
import { Theme } from '@/constants/themes';
import { API_MODELS } from '@/constants';
import { UserSettings } from '@/types';

interface NotificationSettings {
  studyReminders: boolean;
  quizCompletion: boolean;
  streakMilestones: boolean;
  reminderTime: string;
}

export const SettingsScreen: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const styles = useThemedStyles(createStyles);
  const [settings, setSettings] = useState<UserSettings>({
    language: 'ar',
    theme: 'light',
  });
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [notifications, setNotifications] = useState<NotificationSettings>({
    studyReminders: true,
    quizCompletion: true,
    streakMilestones: true,
    reminderTime: '19:00',
  });
  const [loading, setLoading] = useState(false);
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [newApiKey, setNewApiKey] = useState({
    name: '',
    key: '',
    model: API_MODELS.GEMINI_PRO,
  });

  useEffect(() => {
    loadSettings();
    loadApiKeys();
    loadNotificationSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const userSettings = await AsyncStorageService.getUserSettings();
      setSettings(userSettings);
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const loadApiKeys = async () => {
    try {
      const keys = await ApiKeyService.loadApiKeys();
      setApiKeys(keys);
    } catch (error) {
      console.error('Error loading API keys:', error);
    }
  };

  const loadNotificationSettings = async () => {
    try {
      // Load from AsyncStorage or use defaults
      const stored = await AsyncStorageService.getItem('notification_settings');
      if (stored) {
        setNotifications(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Error loading notification settings:', error);
    }
  };

  const handleLanguageChange = async (language: 'ar' | 'en') => {
    try {
      const newSettings = { ...settings, language };
      setSettings(newSettings);
      await AsyncStorageService.saveUserSettings(newSettings);
      await i18n.changeLanguage(language);
    } catch (error) {
      console.error('Error changing language:', error);
      Alert.alert(t('common.error'), 'Failed to change language');
    }
  };

  const handleAddApiKey = async () => {
    if (!newApiKey.name.trim() || !newApiKey.key.trim()) {
      Alert.alert(t('common.error'), 'Please fill in all fields');
      return;
    }

    if (!ApiKeyService.isValidApiKeyFormat(newApiKey.key)) {
      Alert.alert(t('common.error'), 'Invalid API key format');
      return;
    }

    try {
      setLoading(true);
      const validation = await ApiKeyService.validateApiKey(newApiKey.key, newApiKey.model);

      if (!validation.isValid) {
        Alert.alert(t('common.error'), validation.error || 'Invalid API key');
        return;
      }

      await ApiKeyService.addApiKey({
        name: newApiKey.name,
        key: newApiKey.key,
        model: newApiKey.model,
        isDefault: apiKeys.length === 0,
      });

      setNewApiKey({ name: '', key: '', model: API_MODELS.GEMINI_PRO });
      setShowApiKeyModal(false);
      await loadApiKeys();
      Alert.alert(t('common.success'), 'API key added successfully');
    } catch (error) {
      console.error('Error adding API key:', error);
      Alert.alert(t('common.error'), 'Failed to add API key');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteApiKey = (id: string) => {
    Alert.alert(
      'Delete API Key',
      'Are you sure you want to delete this API key?',
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await ApiKeyService.deleteApiKey(id);
              await loadApiKeys();
              Alert.alert(t('common.success'), 'API key deleted');
            } catch (error) {
              Alert.alert(t('common.error'), 'Failed to delete API key');
            }
          },
        },
      ]
    );
  };

  const handleSetDefaultApiKey = async (id: string) => {
    try {
      await ApiKeyService.updateApiKey(id, { isDefault: true });
      await loadApiKeys();
    } catch (error) {
      Alert.alert(t('common.error'), 'Failed to set default API key');
    }
  };

  const handleNotificationChange = async (key: keyof NotificationSettings, value: boolean | string) => {
    try {
      const newNotifications = { ...notifications, [key]: value };
      setNotifications(newNotifications);
      await AsyncStorageService.setItem('notification_settings', JSON.stringify(newNotifications));
    } catch (error) {
      console.error('Error updating notifications:', error);
    }
  };

  const handleClearData = () => {
    Alert.alert(
      t('settings.clearData'),
      t('settings.confirmClearData'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.ok'),
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              await DatabaseService.clearAllData();
              await AsyncStorageService.clearAllData();
              Alert.alert(t('common.success'), 'All data cleared successfully');
            } catch (error) {
              console.error('Error clearing data:', error);
              Alert.alert(t('common.error'), 'Failed to clear data');
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  const renderSectionHeader = (title: string, iconName: string, iconLibrary: any = 'MaterialCommunityIcons') => (
    <View style={styles.sectionHeader}>
      <LinearGradient
        colors={[theme.colors.primary, theme.colors.primaryDark]}
        style={styles.sectionHeaderGradient}
      >
        <Icon
          name={iconName}
          library={iconLibrary}
          size={ICON_SIZES.md}
          color={theme.colors.textOnPrimary}
        />
        <Text style={styles.sectionHeaderTitle}>{title}</Text>
      </LinearGradient>
    </View>
  );

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      {/* Language Settings */}
      {renderSectionHeader(t('settings.language'), 'translate')}
      <View style={styles.section}>
        <View style={styles.languageContainer}>
          <TouchableOpacity
            style={[
              styles.languageOption,
              settings.language === 'ar' && styles.languageOptionActive
            ]}
            onPress={() => handleLanguageChange('ar')}
          >
            <LinearGradient
              colors={settings.language === 'ar'
                ? [theme.colors.primary, theme.colors.primaryDark]
                : [theme.colors.cardBackground, theme.colors.surfaceSecondary]
              }
              style={styles.languageOptionGradient}
            >
              <Text style={[
                styles.languageOptionText,
                settings.language === 'ar' && styles.languageOptionTextActive
              ]}>
                العربية
              </Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.languageOption,
              settings.language === 'en' && styles.languageOptionActive
            ]}
            onPress={() => handleLanguageChange('en')}
          >
            <LinearGradient
              colors={settings.language === 'en'
                ? [theme.colors.primary, theme.colors.primaryDark]
                : [theme.colors.cardBackground, theme.colors.surfaceSecondary]
              }
              style={styles.languageOptionGradient}
            >
              <Text style={[
                styles.languageOptionText,
                settings.language === 'en' && styles.languageOptionTextActive
              ]}>
                English
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>

      {/* Theme Settings */}
      {renderSectionHeader(t('settings.appearance'), 'palette')}
      <View style={styles.section}>
        <ThemeToggle
          variant="card"
          onToggle={(isDarkMode) => {
            console.log('Theme toggled to:', isDarkMode ? 'dark' : 'light');
          }}
        />
      </View>

      {/* API Key Management */}
      {renderSectionHeader('API Keys', 'key-variant')}
      <View style={styles.section}>
        <View style={styles.apiKeyHeader}>
          <TouchableOpacity
            style={styles.addKeyButton}
            onPress={() => setShowApiKeyModal(true)}
          >
            <LinearGradient
              colors={[theme.colors.secondary, theme.colors.secondaryDark]}
              style={styles.addKeyButtonGradient}
            >
              <Icon
                name="plus"
                library="MaterialCommunityIcons"
                size={ICON_SIZES.sm}
                color={theme.colors.textOnPrimary}
              />
              <Text style={styles.addKeyButtonText}>Add Key</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
        <View style={styles.apiKeysList}>
          {apiKeys.map((apiKey) => (
            <View key={apiKey.id} style={styles.apiKeyItem}>
              <View style={styles.apiKeyInfo}>
                <Text style={styles.apiKeyName}>{apiKey.name}</Text>
                <Text style={styles.apiKeyModel}>{apiKey.model}</Text>
                <Text style={styles.apiKeyPreview}>
                  {apiKey.key.substring(0, 10)}...
                </Text>
                {apiKey.isDefault && (
                  <Text style={styles.defaultBadge}>Default</Text>
                )}
              </View>
              <View style={styles.apiKeyActions}>
                {!apiKey.isDefault && (
                  <TouchableOpacity
                    onPress={() => handleSetDefaultApiKey(apiKey.id)}
                    style={styles.actionButton}
                  >
                    <Text style={styles.actionButtonText}>Set Default</Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  onPress={() => handleDeleteApiKey(apiKey.id)}
                  style={[styles.actionButton, styles.deleteButton]}
                >
                  <Text style={[styles.actionButtonText, styles.deleteButtonText]}>Delete</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}
          {apiKeys.length === 0 && (
            <Text style={styles.emptyText}>No API keys configured</Text>
          )}
        </View>
      </View>

      {/* Notification Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notifications</Text>
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Study Reminders</Text>
          <Switch
            value={notifications.studyReminders}
            onValueChange={(value) => handleNotificationChange('studyReminders', value)}
            trackColor={{ false: theme.colors.border, true: theme.colors.primaryLight }}
            thumbColor={notifications.studyReminders ? theme.colors.primary : theme.colors.textSecondary}
          />
        </View>
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Quiz Completion</Text>
          <Switch
            value={notifications.quizCompletion}
            onValueChange={(value) => handleNotificationChange('quizCompletion', value)}
            trackColor={{ false: theme.colors.border, true: theme.colors.primaryLight }}
            thumbColor={notifications.quizCompletion ? theme.colors.primary : theme.colors.textSecondary}
          />
        </View>
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Streak Milestones</Text>
          <Switch
            value={notifications.streakMilestones}
            onValueChange={(value) => handleNotificationChange('streakMilestones', value)}
            trackColor={{ false: theme.colors.border, true: theme.colors.primaryLight }}
            thumbColor={notifications.streakMilestones ? theme.colors.primary : theme.colors.textSecondary}
          />
        </View>
      </View>

      {/* Data Management */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Data Management</Text>
        <Button
          title={t('settings.clearData')}
          onPress={handleClearData}
          variant="danger"
          loading={loading}
          disabled={loading}
        />
      </View>

      {/* About */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t('settings.about')}</Text>
        <View style={styles.infoContainer}>
          <Text style={styles.infoText}>
            {t('app.name')} - {t('app.tagline')}
          </Text>
          <Text style={styles.versionText}>
            {t('settings.version')}: 1.0.0
          </Text>
        </View>
      </View>

      {/* API Key Modal */}
      <Modal
        visible={showApiKeyModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowApiKeyModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Add API Key</Text>
            <TouchableOpacity
              onPress={() => setShowApiKeyModal(false)}
              style={styles.closeButton}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Name</Text>
              <TextInput
                style={styles.textInput}
                value={newApiKey.name}
                onChangeText={(text) => setNewApiKey({ ...newApiKey, name: text })}
                placeholder="e.g., My Gemini Key"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>API Key</Text>
              <TextInput
                style={styles.textInput}
                value={newApiKey.key}
                onChangeText={(text) => setNewApiKey({ ...newApiKey, key: text })}
                placeholder="AIza..."
                placeholderTextColor={theme.colors.textSecondary}
                secureTextEntry
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Model</Text>
              <View style={styles.modelSelector}>
                {Object.values(API_MODELS).map((model) => (
                  <TouchableOpacity
                    key={model}
                    style={[
                      styles.modelOption,
                      newApiKey.model === model && styles.selectedModel
                    ]}
                    onPress={() => setNewApiKey({ ...newApiKey, model })}
                  >
                    <Text style={[
                      styles.modelOptionText,
                      newApiKey.model === model && styles.selectedModelText
                    ]}>
                      {model}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.helpSection}>
              <Text style={styles.helpTitle}>How to get a Gemini API Key:</Text>
              <Text style={styles.helpText}>
                1. Go to Google AI Studio (ai.google.dev){'\n'}
                2. Sign in with your Google account{'\n'}
                3. Click "Get API Key"{'\n'}
                4. Create a new API key{'\n'}
                5. Copy and paste it here
              </Text>
            </View>

            <View style={styles.modalActions}>
              <Button
                title="Cancel"
                onPress={() => setShowApiKeyModal(false)}
                variant="outline"
                style={styles.modalButton}
              />
              <Button
                title="Add Key"
                onPress={handleAddApiKey}
                loading={loading}
                disabled={loading}
                style={styles.modalButton}
              />
            </View>
          </ScrollView>
        </View>
      </Modal>
    </ScrollView>
  );
};

const createStyles = (theme: Theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    padding: 20,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: theme.typography.fontFamily.bold,
    color: theme.colors.text,
    marginBottom: 16,
  },
  sectionHeaderOld: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  buttonGroup: {
    flexDirection: 'row',
    gap: 12,
  },
  languageButton: {
    flex: 1,
  },
  // API Keys Styles
  apiKeysList: {
    gap: 12,
  },
  apiKeyItem: {
    backgroundColor: theme.colors.cardBackground,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
    ...theme.shadows.md,
  },
  apiKeyInfo: {
    marginBottom: 12,
  },
  apiKeyName: {
    fontSize: 16,
    fontFamily: theme.typography.fontFamily.medium,
    color: theme.colors.text,
    marginBottom: 4,
  },
  apiKeyModel: {
    fontSize: 14,
    fontFamily: theme.typography.fontFamily.regular,
    color: theme.colors.primary,
    marginBottom: 4,
  },
  apiKeyPreview: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: theme.colors.textSecondary,
  },
  defaultBadge: {
    fontSize: 12,
    fontFamily: theme.typography.fontFamily.medium,
    color: theme.colors.primary,
    backgroundColor: theme.colors.surfaceSecondary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  apiKeyActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: theme.colors.surfaceSecondary,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
  },
  actionButtonText: {
    fontSize: 12,
    fontFamily: theme.typography.fontFamily.medium,
    color: theme.colors.primary,
  },
  deleteButton: {
    backgroundColor: theme.colors.error,
  },
  deleteButtonText: {
    color: theme.colors.surface,
  },
  emptyText: {
    fontSize: 14,
    fontFamily: theme.typography.fontFamily.regular,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
    padding: 20,
  },
  // Notification Settings
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.cardBackground,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
    ...theme.shadows.sm,
  },
  settingLabel: {
    fontSize: 16,
    fontFamily: theme.typography.fontFamily.regular,
    color: theme.colors.text,
  },
  // Info Container
  infoContainer: {
    backgroundColor: theme.colors.cardBackground,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
    ...theme.shadows.md,
  },
  infoText: {
    fontSize: 16,
    fontFamily: theme.typography.fontFamily.regular,
    color: theme.colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  versionText: {
    fontSize: 14,
    fontFamily: theme.typography.fontFamily.regular,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.modalBackground,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
    backgroundColor: theme.colors.primaryDark,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: theme.typography.fontFamily.bold,
    color: theme.colors.textOnPrimary,
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 18,
    color: theme.colors.textOnPrimary,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontFamily: theme.typography.fontFamily.medium,
    color: theme.colors.text,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    fontFamily: theme.typography.fontFamily.regular,
    color: theme.colors.textOnSurface,
    backgroundColor: theme.colors.cardBackground,
    ...theme.shadows.sm,
  },
  modelSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  modelOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
    backgroundColor: theme.colors.cardBackground,
  },
  selectedModel: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  modelOptionText: {
    fontSize: 14,
    fontFamily: theme.typography.fontFamily.regular,
    color: theme.colors.text,
  },
  selectedModelText: {
    color: theme.colors.textOnPrimary,
  },
  helpSection: {
    backgroundColor: COLORS.surfaceTertiary,
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: COLORS.borderLight,
  },
  helpTitle: {
    fontSize: 16,
    fontFamily: FONTS.medium,
    color: COLORS.text,
    marginBottom: 8,
  },
  helpText: {
    fontSize: 14,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    lineHeight: 20,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 20,
  },
  modalButton: {
    flex: 1,
  },
  // Professional Section Headers
  sectionHeader: {
    marginBottom: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    ...SHADOWS.md,
  },
  sectionHeaderGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  sectionHeaderTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontFamily: FONTS.semiBold,
    color: COLORS.textOnPrimary,
    marginLeft: SPACING.md,
  },
  // Enhanced Language Selection
  languageContainer: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  languageOption: {
    flex: 1,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    ...SHADOWS.sm,
  },
  languageOptionActive: {
    ...SHADOWS.md,
  },
  languageOptionGradient: {
    paddingVertical: SPACING.lg,
    paddingHorizontal: SPACING.xl,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.borderLight,
  },
  languageOptionText: {
    fontSize: TYPOGRAPHY.fontSize.base,
    fontFamily: FONTS.medium,
    color: COLORS.text,
  },
  languageOptionTextActive: {
    color: COLORS.textOnPrimary,
  },
  // API Key Section
  apiKeyHeader: {
    alignItems: 'flex-end',
    marginBottom: SPACING.lg,
  },
  addKeyButton: {
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
    ...SHADOWS.sm,
  },
  addKeyButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    gap: SPACING.xs,
  },
  addKeyButtonText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontFamily: FONTS.medium,
    color: COLORS.textOnPrimary,
  },
});
