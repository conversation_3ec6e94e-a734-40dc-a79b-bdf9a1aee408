import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { HomeScreen } from '@/screens/HomeScreen';
import { QuizSetupScreen } from '@/screens/QuizSetupScreen';
import { QuizScreen } from '@/screens/QuizScreen';
import { QuizResultsScreen } from '@/screens/QuizResultsScreen';
import { HistoryScreen } from '@/screens/HistoryScreen';
import { SettingsScreen } from '@/screens/SettingsScreen';
import { ChatScreen } from '@/screens/ChatScreen';
import { Icon, ICON_SIZES } from '@/components/ui/Icon';
import { COLORS, TYPOGRAPHY, SPACING } from '@/constants';
import { useTranslation } from 'react-i18next';

export type RootStackParamList = {
  MainTabs: undefined;
  QuizSetup: {
    file: {
      uri: string;
      name: string;
      type: 'pdf' | 'image';
      size: number;
    };
  };
  Quiz: {
    quizId: string;
  };
  QuizResults: {
    quizId: string;
  };
};

export type TabParamList = {
  Home: undefined;
  History: undefined;
  Chat: undefined;
  Settings: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<TabParamList>();

const TabNavigator = () => {
  const { t } = useTranslation();

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: COLORS.primary,
        tabBarInactiveTintColor: COLORS.textSecondary,
        tabBarStyle: {
          backgroundColor: COLORS.cardBackground,
          borderTopColor: COLORS.borderLight,
          shadowColor: COLORS.primaryDark,
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 8,
        },
        headerStyle: {
          backgroundColor: COLORS.primaryDeep,
          shadowColor: COLORS.primaryVeryDark,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.2,
          shadowRadius: 4,
          elevation: 8,
        },
        headerTintColor: COLORS.textOnPrimary,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: t('navigation.home'),
          tabBarLabel: t('navigation.home'),
          tabBarIcon: ({ color, size }) => (
            <Icon
              name="home-outline"
              library="MaterialCommunityIcons"
              size={size}
              color={color}
            />
          ),
        }}
      />
      <Tab.Screen
        name="History"
        component={HistoryScreen}
        options={{
          title: t('navigation.history'),
          tabBarLabel: t('navigation.history'),
          tabBarIcon: ({ color, size }) => (
            <Icon
              name="chart-line"
              library="MaterialCommunityIcons"
              size={size}
              color={color}
            />
          ),
        }}
      />
      <Tab.Screen
        name="Chat"
        component={ChatScreen}
        options={{
          title: t('navigation.chat'),
          tabBarLabel: t('navigation.chat'),
          tabBarIcon: ({ color, size }) => (
            <Icon
              name="robot-outline"
              library="MaterialCommunityIcons"
              size={size}
              color={color}
            />
          ),
        }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          title: t('navigation.settings'),
          tabBarLabel: t('navigation.settings'),
          tabBarIcon: ({ color, size }) => (
            <Icon
              name="cog-outline"
              library="MaterialCommunityIcons"
              size={size}
              color={color}
            />
          ),
        }}
      />
    </Tab.Navigator>
  );
};

export const AppNavigator = () => {
  const { t } = useTranslation();

  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="MainTabs"
        screenOptions={{
          headerStyle: {
            backgroundColor: COLORS.primary,
          },
          headerTintColor: COLORS.surface,
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen
          name="MainTabs"
          component={TabNavigator}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="QuizSetup"
          component={QuizSetupScreen}
          options={{
            title: t('quizSetup.title'),
            headerBackTitle: t('navigation.home'),
          }}
        />
        <Stack.Screen
          name="Quiz"
          component={QuizScreen}
          options={{
            title: t('navigation.quiz'),
            headerBackTitle: t('navigation.home'),
            headerLeft: () => null, // Prevent going back during quiz
          }}
        />
        <Stack.Screen
          name="QuizResults"
          component={QuizResultsScreen}
          options={{
            title: t('results.title'),
            headerBackTitle: t('navigation.home'),
            headerLeft: () => null, // Prevent going back to quiz
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};
