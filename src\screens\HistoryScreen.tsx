import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ScrollView,
  Animated,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import { DatabaseService } from '@/services/storage/database';
import { Button } from '@/components/ui/Button';
import { Icon, ICON_SIZES } from '@/components/ui/Icon';
import { COLORS, FONTS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS } from '@/constants';
import { QuizHistoryItem } from '@/types';
import { formatDate, formatScore } from '@/utils';

interface AnalyticsData {
  totalQuizzes: number;
  averageScore: number;
  bestScore: number;
  totalQuestions: number;
  recentTrend: 'up' | 'down' | 'stable';
}

export const HistoryScreen: React.FC = () => {
  const { t } = useTranslation();
  const [history, setHistory] = useState<QuizHistoryItem[]>([]);
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    totalQuizzes: 0,
    averageScore: 0,
    bestScore: 0,
    totalQuestions: 0,
    recentTrend: 'stable',
  });
  const [loading, setLoading] = useState(true);
  const fadeAnim = useState(new Animated.Value(0))[0];

  useEffect(() => {
    loadHistory();
  }, []);

  const loadHistory = async () => {
    try {
      setLoading(true);
      const historyData = await DatabaseService.getQuizHistory();
      setHistory(historyData);
      calculateAnalytics(historyData);
    } catch (error) {
      console.error('Error loading history:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateAnalytics = (quizHistory: QuizHistoryItem[]) => {
    if (quizHistory.length === 0) return;

    const totalQuizzes = quizHistory.length;
    const totalQuestions = quizHistory.reduce((sum, quiz) => sum + quiz.totalQuestions, 0);
    const averageScore = Math.round(
      quizHistory.reduce((sum, quiz) => sum + quiz.score, 0) / totalQuizzes
    );
    const bestScore = Math.max(...quizHistory.map(quiz => quiz.score));

    // Calculate recent trend (last 3 vs previous 3)
    let recentTrend: 'up' | 'down' | 'stable' = 'stable';
    if (quizHistory.length >= 6) {
      const recent3 = quizHistory.slice(0, 3).reduce((sum, quiz) => sum + quiz.score, 0) / 3;
      const previous3 = quizHistory.slice(3, 6).reduce((sum, quiz) => sum + quiz.score, 0) / 3;

      if (recent3 > previous3 + 5) recentTrend = 'up';
      else if (recent3 < previous3 - 5) recentTrend = 'down';
    }

    setAnalytics({
      totalQuizzes,
      averageScore,
      bestScore,
      totalQuestions,
      recentTrend,
    });
  };

  const handleDeleteQuiz = async (quizId: string) => {
    Alert.alert(
      t('history.delete'),
      t('history.confirmDelete'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await DatabaseService.deleteQuiz(quizId);
              await loadHistory(); // Refresh the list
            } catch (error) {
              console.error('Error deleting quiz:', error);
              Alert.alert(t('common.error'), 'Failed to delete quiz');
            }
          },
        },
      ]
    );
  };

  const renderHistoryItem = ({ item }: { item: QuizHistoryItem }) => (
    <View style={styles.historyItem}>
      <View style={styles.historyInfo}>
        <Text style={styles.subject}>{item.subject}</Text>
        <Text style={styles.date}>{formatDate(item.date)}</Text>
        <Text style={styles.score}>
          {formatScore(item.score, item.totalQuestions)}
        </Text>
      </View>
      <View style={styles.actions}>
        <Button
          title={t('history.viewDetails')}
          onPress={() => {
            // TODO: Navigate to quiz details
            console.log('View details for quiz:', item.id);
          }}
          size="small"
          variant="outline"
          style={styles.actionButton}
        />
        <Button
          title={t('history.delete')}
          onPress={() => handleDeleteQuiz(item.id)}
          size="small"
          variant="danger"
          style={styles.actionButton}
        />
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.loadingText}>{t('common.loading')}</Text>
      </View>
    );
  }

  if (history.length === 0) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.emptyText}>{t('history.noHistory')}</Text>
      </View>
    );
  }

  const renderAnalyticsCards = () => (
    <Animated.View style={[styles.analyticsContainer, { opacity: fadeAnim }]}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.analyticsScroll}>
        <View style={styles.analyticsCard}>
          <LinearGradient
            colors={[COLORS.primary, COLORS.primaryDark]}
            style={styles.analyticsCardGradient}
          >
            <Icon name="quiz" library="MaterialCommunityIcons" size={ICON_SIZES.lg} color={COLORS.textOnPrimary} />
            <Text style={styles.analyticsNumber}>{analytics.totalQuizzes}</Text>
            <Text style={styles.analyticsLabel}>Total Quizzes</Text>
          </LinearGradient>
        </View>

        <View style={styles.analyticsCard}>
          <LinearGradient
            colors={[COLORS.secondary, COLORS.secondaryDark]}
            style={styles.analyticsCardGradient}
          >
            <Icon name="chart-line" library="MaterialCommunityIcons" size={ICON_SIZES.lg} color={COLORS.textOnPrimary} />
            <Text style={styles.analyticsNumber}>{analytics.averageScore}%</Text>
            <Text style={styles.analyticsLabel}>Average Score</Text>
          </LinearGradient>
        </View>

        <View style={styles.analyticsCard}>
          <LinearGradient
            colors={[COLORS.accent, '#d946ef']}
            style={styles.analyticsCardGradient}
          >
            <Icon name="trophy" library="MaterialCommunityIcons" size={ICON_SIZES.lg} color={COLORS.textOnPrimary} />
            <Text style={styles.analyticsNumber}>{analytics.bestScore}%</Text>
            <Text style={styles.analyticsLabel}>Best Score</Text>
          </LinearGradient>
        </View>

        <View style={styles.analyticsCard}>
          <LinearGradient
            colors={[COLORS.info, COLORS.primaryDark]}
            style={styles.analyticsCardGradient}
          >
            <Icon
              name={analytics.recentTrend === 'up' ? 'trending-up' : analytics.recentTrend === 'down' ? 'trending-down' : 'trending-neutral'}
              library="MaterialCommunityIcons"
              size={ICON_SIZES.lg}
              color={COLORS.textOnPrimary}
            />
            <Text style={styles.analyticsNumber}>{analytics.totalQuestions}</Text>
            <Text style={styles.analyticsLabel}>Questions</Text>
          </LinearGradient>
        </View>
      </ScrollView>
    </Animated.View>
  );

  return (
    <View style={styles.container}>
      {renderAnalyticsCards()}
      <FlatList
        data={history}
        renderItem={renderHistoryItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  listContent: {
    padding: 20,
    gap: 12,
  },
  historyItem: {
    backgroundColor: COLORS.cardBackground,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.borderLight,
    shadowColor: COLORS.primaryDark,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  historyInfo: {
    marginBottom: 12,
  },
  subject: {
    fontSize: 18,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginBottom: 4,
  },
  date: {
    fontSize: 14,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    marginBottom: 4,
  },
  score: {
    fontSize: 16,
    fontFamily: FONTS.medium,
    color: COLORS.primary,
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
  },
  // Analytics Cards
  analyticsContainer: {
    marginBottom: SPACING.xl,
  },
  analyticsScroll: {
    paddingHorizontal: SPACING.lg,
  },
  analyticsCard: {
    marginRight: SPACING.md,
    borderRadius: BORDER_RADIUS.xl,
    overflow: 'hidden',
    ...SHADOWS.lg,
  },
  analyticsCardGradient: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.xl,
    alignItems: 'center',
    minWidth: 120,
    minHeight: 120,
    justifyContent: 'center',
  },
  analyticsNumber: {
    fontSize: TYPOGRAPHY.fontSize['2xl'],
    fontFamily: FONTS.bold,
    color: COLORS.textOnPrimary,
    marginTop: SPACING.md,
    marginBottom: SPACING.xs,
  },
  analyticsLabel: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontFamily: FONTS.medium,
    color: COLORS.textOnPrimary,
    textAlign: 'center',
    opacity: 0.9,
  },
});
