# Deployment Guide

This guide covers the deployment process for the Test Me (اختبرني) application to various platforms.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Environment Configuration](#environment-configuration)
- [Build Process](#build-process)
- [iOS Deployment](#ios-deployment)
- [Android Deployment](#android-deployment)
- [Web Deployment](#web-deployment)
- [CI/CD Pipeline](#cicd-pipeline)
- [Monitoring & Analytics](#monitoring--analytics)

## Prerequisites

### Development Environment

- **Node.js**: 18.x or higher
- **npm**: 8.x or higher (or yarn 1.22.x+)
- **Expo CLI**: Latest version (`npm install -g @expo/cli`)
- **EAS CLI**: Latest version (`npm install -g eas-cli`)

### Platform-Specific Requirements

#### iOS
- **macOS**: Required for iOS builds
- **Xcode**: Latest stable version
- **Apple Developer Account**: For App Store distribution
- **iOS Simulator**: For testing

#### Android
- **Android Studio**: Latest stable version
- **Java Development Kit**: JDK 11 or higher
- **Android SDK**: API level 33+
- **Google Play Console Account**: For Play Store distribution

## Environment Configuration

### Environment Variables

Create environment files for different deployment stages:

#### `.env.development`
```bash
# Development environment
EXPO_PUBLIC_ENVIRONMENT=development
EXPO_PUBLIC_API_BASE_URL=https://api-dev.testme-app.com
EXPO_PUBLIC_GEMINI_API_KEY=your_dev_gemini_key
EXPO_PUBLIC_ANALYTICS_ENABLED=false
EXPO_PUBLIC_DEBUG_MODE=true
```

#### `.env.staging`
```bash
# Staging environment
EXPO_PUBLIC_ENVIRONMENT=staging
EXPO_PUBLIC_API_BASE_URL=https://api-staging.testme-app.com
EXPO_PUBLIC_GEMINI_API_KEY=your_staging_gemini_key
EXPO_PUBLIC_ANALYTICS_ENABLED=true
EXPO_PUBLIC_DEBUG_MODE=false
```

#### `.env.production`
```bash
# Production environment
EXPO_PUBLIC_ENVIRONMENT=production
EXPO_PUBLIC_API_BASE_URL=https://api.testme-app.com
EXPO_PUBLIC_GEMINI_API_KEY=your_production_gemini_key
EXPO_PUBLIC_ANALYTICS_ENABLED=true
EXPO_PUBLIC_DEBUG_MODE=false
EXPO_PUBLIC_SENTRY_DSN=your_sentry_dsn
```

### App Configuration

Update `app.json` for different environments:

```json
{
  "expo": {
    "name": "Test Me",
    "slug": "test-me-app",
    "version": "2.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "automatic",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#8B5CF6"
    },
    "assetBundlePatterns": [
      "**/*"
    ],
    "ios": {
      "supportsTablet": true,
      "bundleIdentifier": "com.testme.app",
      "buildNumber": "1",
      "infoPlist": {
        "NSCameraUsageDescription": "This app uses camera to scan documents for quiz generation.",
        "NSPhotoLibraryUsageDescription": "This app accesses photo library to select images for quiz generation."
      }
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#8B5CF6"
      },
      "package": "com.testme.app",
      "versionCode": 1,
      "permissions": [
        "android.permission.CAMERA",
        "android.permission.READ_EXTERNAL_STORAGE"
      ]
    },
    "web": {
      "favicon": "./assets/favicon.png",
      "bundler": "metro"
    },
    "plugins": [
      "expo-localization",
      "expo-font",
      [
        "expo-document-picker",
        {
          "iCloudContainerEnvironment": "Production"
        }
      ]
    ],
    "extra": {
      "eas": {
        "projectId": "your-eas-project-id"
      }
    }
  }
}
```

## Build Process

### Pre-build Checklist

1. **Update Version Numbers**
   ```bash
   # Update package.json version
   npm version patch|minor|major
   
   # Update app.json version and build numbers
   # iOS: increment buildNumber
   # Android: increment versionCode
   ```

2. **Run Tests**
   ```bash
   npm test
   npm run lint
   npm run type-check
   ```

3. **Update Documentation**
   - Update CHANGELOG.md
   - Update README.md if needed
   - Update API documentation

### EAS Build Configuration

Create `eas.json` for build configuration:

```json
{
  "cli": {
    "version": ">= 5.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "env": {
        "ENVIRONMENT": "development"
      }
    },
    "preview": {
      "distribution": "internal",
      "env": {
        "ENVIRONMENT": "staging"
      }
    },
    "production": {
      "env": {
        "ENVIRONMENT": "production"
      }
    }
  },
  "submit": {
    "production": {}
  }
}
```

## iOS Deployment

### Development Build

```bash
# Build for iOS simulator
eas build --platform ios --profile development

# Install on simulator
eas build:run --platform ios --latest
```

### TestFlight Distribution

```bash
# Build for TestFlight
eas build --platform ios --profile preview

# Submit to TestFlight
eas submit --platform ios --latest
```

### App Store Release

```bash
# Production build
eas build --platform ios --profile production

# Submit to App Store
eas submit --platform ios --latest
```

### iOS-Specific Considerations

1. **App Store Guidelines**
   - Ensure compliance with App Store Review Guidelines
   - Test on multiple iOS versions and devices
   - Verify accessibility features work correctly

2. **Privacy Requirements**
   - Update privacy policy for data collection
   - Configure App Tracking Transparency if needed
   - Review and update Info.plist permissions

3. **Performance Optimization**
   - Test app launch time (< 20 seconds)
   - Optimize bundle size
   - Test memory usage on older devices

## Android Deployment

### Development Build

```bash
# Build for Android
eas build --platform android --profile development

# Install on device/emulator
eas build:run --platform android --latest
```

### Internal Testing

```bash
# Build for internal testing
eas build --platform android --profile preview

# Submit to Google Play Console (Internal Testing)
eas submit --platform android --latest
```

### Production Release

```bash
# Production build
eas build --platform android --profile production

# Submit to Google Play Store
eas submit --platform android --latest
```

### Android-Specific Considerations

1. **Google Play Requirements**
   - Target latest Android API level
   - Comply with Google Play policies
   - Test on various Android versions and devices

2. **App Bundle Optimization**
   - Use Android App Bundle (AAB) format
   - Enable Play Asset Delivery if needed
   - Optimize APK size

3. **Permissions**
   - Request permissions at runtime
   - Provide clear permission rationale
   - Handle permission denials gracefully

## Web Deployment

### Build for Web

```bash
# Build web version
npx expo export:web

# Serve locally for testing
npx serve web-build
```

### Deployment Options

#### Netlify
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Deploy to Netlify
netlify deploy --prod --dir web-build
```

#### Vercel
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy to Vercel
vercel --prod web-build
```

#### Custom Server
```bash
# Build and copy to server
npx expo export:web
scp -r web-build/* user@server:/var/www/testme-app/
```

## CI/CD Pipeline

### GitHub Actions Workflow

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy App

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: npm
      
      - run: npm ci
      - run: npm test
      - run: npm run lint
      - run: npm run type-check

  build-ios:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: npm
      
      - run: npm ci
      - uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      
      - run: eas build --platform ios --profile production --non-interactive

  build-android:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: npm
      
      - run: npm ci
      - uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      
      - run: eas build --platform android --profile production --non-interactive
```

### Environment Secrets

Configure the following secrets in your CI/CD platform:

- `EXPO_TOKEN`: Expo authentication token
- `APPLE_ID`: Apple ID for App Store submissions
- `APPLE_APP_SPECIFIC_PASSWORD`: App-specific password
- `GOOGLE_SERVICE_ACCOUNT_KEY`: Google Play service account key

## Monitoring & Analytics

### Error Tracking

Configure Sentry for error tracking:

```bash
npm install @sentry/react-native
```

```typescript
import * as Sentry from '@sentry/react-native';

Sentry.init({
  dsn: process.env.EXPO_PUBLIC_SENTRY_DSN,
  environment: process.env.EXPO_PUBLIC_ENVIRONMENT,
});
```

### Performance Monitoring

1. **App Performance**
   - Monitor app launch time
   - Track screen load times
   - Monitor memory usage

2. **User Analytics**
   - Track user engagement
   - Monitor feature usage
   - Analyze user flows

3. **Business Metrics**
   - Quiz completion rates
   - User retention
   - Feature adoption

### Health Checks

Implement health check endpoints:

```typescript
// Health check for API services
export const healthCheck = async (): Promise<HealthStatus> => {
  try {
    // Check API connectivity
    // Check database connectivity
    // Check external services
    
    return { status: 'healthy', timestamp: new Date() };
  } catch (error) {
    return { status: 'unhealthy', error: error.message, timestamp: new Date() };
  }
};
```

## Post-Deployment

### Verification Checklist

- [ ] App launches successfully
- [ ] All core features work
- [ ] API integrations functional
- [ ] Analytics tracking working
- [ ] Error reporting configured
- [ ] Performance metrics normal
- [ ] User feedback channels active

### Rollback Plan

1. **Immediate Issues**
   - Revert to previous version
   - Communicate with users
   - Fix issues in hotfix branch

2. **Gradual Rollout**
   - Use staged rollouts
   - Monitor metrics closely
   - Increase rollout percentage gradually

### Support

- Monitor app store reviews
- Respond to user feedback
- Track support tickets
- Update documentation as needed

For additional support, contact the development <NAME_EMAIL>.
