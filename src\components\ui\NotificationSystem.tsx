import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
  Dimensions,
  PanGestureHandler,
  State,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { COLORS, SPACING, BORDER_RADIUS, TYPOGRAPHY } from '@/constants';

const { width: screenWidth } = Dimensions.get('window');

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  action?: {
    label: string;
    onPress: () => void;
  };
  persistent?: boolean;
}

interface NotificationItemProps {
  notification: Notification;
  onDismiss: (id: string) => void;
  index: number;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onDismiss,
  index,
}) => {
  const { theme } = useTheme();
  const translateY = React.useRef(new Animated.Value(-100)).current;
  const translateX = React.useRef(new Animated.Value(0)).current;
  const opacity = React.useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto dismiss
    if (!notification.persistent) {
      const timer = setTimeout(() => {
        dismissNotification();
      }, notification.duration || 4000);

      return () => clearTimeout(timer);
    }
  }, []);

  const dismissNotification = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: -100,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onDismiss(notification.id);
    });
  };

  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    { useNativeDriver: true }
  );

  const onHandlerStateChange = (event: any) => {
    if (event.nativeEvent.state === State.END) {
      const { translationX, velocityX } = event.nativeEvent;
      
      if (Math.abs(translationX) > screenWidth * 0.3 || Math.abs(velocityX) > 500) {
        // Swipe to dismiss
        Animated.timing(translateX, {
          toValue: translationX > 0 ? screenWidth : -screenWidth,
          duration: 200,
          useNativeDriver: true,
        }).start(() => {
          onDismiss(notification.id);
        });
      } else {
        // Snap back
        Animated.spring(translateX, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      }
    }
  };

  const getTypeConfig = () => {
    switch (notification.type) {
      case 'success':
        return {
          backgroundColor: theme.colors.successLight,
          borderColor: theme.colors.success,
          iconName: 'checkmark-circle' as const,
          iconColor: theme.colors.success,
        };
      case 'error':
        return {
          backgroundColor: theme.colors.errorLight,
          borderColor: theme.colors.error,
          iconName: 'alert-circle' as const,
          iconColor: theme.colors.error,
        };
      case 'warning':
        return {
          backgroundColor: theme.colors.warningLight,
          borderColor: theme.colors.warning,
          iconName: 'warning' as const,
          iconColor: theme.colors.warning,
        };
      case 'info':
        return {
          backgroundColor: theme.colors.infoLight,
          borderColor: theme.colors.info,
          iconName: 'information-circle' as const,
          iconColor: theme.colors.info,
        };
    }
  };

  const typeConfig = getTypeConfig();

  return (
    <PanGestureHandler
      onGestureEvent={onGestureEvent}
      onHandlerStateChange={onHandlerStateChange}
    >
      <Animated.View
        style={[
          styles.notificationContainer,
          {
            backgroundColor: typeConfig.backgroundColor,
            borderColor: typeConfig.borderColor,
            transform: [
              { translateY },
              { translateX },
            ],
            opacity,
            top: 60 + index * 80, // Stack notifications
          },
        ]}
      >
        <View style={styles.notificationContent}>
          <Ionicons
            name={typeConfig.iconName}
            size={24}
            color={typeConfig.iconColor}
            style={styles.notificationIcon}
          />
          
          <View style={styles.notificationText}>
            <Text style={[styles.notificationTitle, { color: theme.colors.text }]}>
              {notification.title}
            </Text>
            <Text style={[styles.notificationMessage, { color: theme.colors.textSecondary }]}>
              {notification.message}
            </Text>
          </View>

          <View style={styles.notificationActions}>
            {notification.action && (
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: typeConfig.iconColor }]}
                onPress={notification.action.onPress}
              >
                <Text style={[styles.actionButtonText, { color: theme.colors.textOnPrimary }]}>
                  {notification.action.label}
                </Text>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity
              style={styles.dismissButton}
              onPress={dismissNotification}
            >
              <Ionicons name="close" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>
        </View>
      </Animated.View>
    </PanGestureHandler>
  );
};

interface NotificationSystemProps {
  notifications: Notification[];
  onDismiss: (id: string) => void;
}

export const NotificationSystem: React.FC<NotificationSystemProps> = ({
  notifications,
  onDismiss,
}) => {
  return (
    <View style={styles.container} pointerEvents="box-none">
      {notifications.map((notification, index) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onDismiss={onDismiss}
          index={index}
        />
      ))}
    </View>
  );
};

// Hook for managing notifications
export const useNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const showNotification = (notification: Omit<Notification, 'id'>) => {
    const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newNotification: Notification = {
      ...notification,
      id,
    };

    setNotifications(prev => [...prev, newNotification]);
    return id;
  };

  const dismissNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  // Convenience methods
  const showSuccess = (title: string, message: string, action?: Notification['action']) => {
    return showNotification({ title, message, type: 'success', action });
  };

  const showError = (title: string, message: string, action?: Notification['action']) => {
    return showNotification({ title, message, type: 'error', action, duration: 6000 });
  };

  const showWarning = (title: string, message: string, action?: Notification['action']) => {
    return showNotification({ title, message, type: 'warning', action });
  };

  const showInfo = (title: string, message: string, action?: Notification['action']) => {
    return showNotification({ title, message, type: 'info', action });
  };

  return {
    notifications,
    showNotification,
    dismissNotification,
    clearAllNotifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  };
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 9999,
  },
  notificationContainer: {
    position: 'absolute',
    left: SPACING.md,
    right: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    shadowColor: COLORS.shadowDark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  notificationContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: SPACING.md,
  },
  notificationIcon: {
    marginRight: SPACING.md,
    marginTop: 2,
  },
  notificationText: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: TYPOGRAPHY.fontSize.base,
    fontWeight: '600',
    marginBottom: SPACING.xs,
  },
  notificationMessage: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    lineHeight: TYPOGRAPHY.lineHeight.normal * TYPOGRAPHY.fontSize.sm,
  },
  notificationActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  actionButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
  },
  actionButtonText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
  },
  dismissButton: {
    padding: SPACING.sm,
  },
});
