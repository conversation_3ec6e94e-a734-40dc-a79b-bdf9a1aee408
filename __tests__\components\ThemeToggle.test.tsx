import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { AsyncStorageService } from '@/services/storage/asyncStorage';
import { AnalyticsService } from '@/services/analytics/analyticsService';

// Mock dependencies
jest.mock('@/services/storage/asyncStorage');
jest.mock('@/services/analytics/analyticsService');
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: { language: 'en' },
  }),
}));

const MockedAsyncStorageService = AsyncStorageService as jest.Mocked<typeof AsyncStorageService>;
const MockedAnalyticsService = AnalyticsService as jest.Mocked<typeof AnalyticsService>;

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode; initialTheme?: 'light' | 'dark' }> = ({ 
  children, 
  initialTheme = 'light' 
}) => (
  <ThemeProvider initialTheme={initialTheme}>
    {children}
  </ThemeProvider>
);

describe('ThemeToggle Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    MockedAsyncStorageService.getItem.mockResolvedValue(null);
    MockedAsyncStorageService.setItem.mockResolvedValue();
    MockedAnalyticsService.trackEvent.mockResolvedValue();
  });

  describe('Rendering', () => {
    it('should render default variant correctly', () => {
      const { getByText } = render(
        <TestWrapper>
          <ThemeToggle />
        </TestWrapper>
      );

      expect(getByText('settings.theme')).toBeTruthy();
      expect(getByText('settings.lightMode')).toBeTruthy();
    });

    it('should render compact variant correctly', () => {
      const { queryByText } = render(
        <TestWrapper>
          <ThemeToggle variant="compact" showLabel={false} />
        </TestWrapper>
      );

      expect(queryByText('settings.theme')).toBeNull();
    });

    it('should render card variant correctly', () => {
      const { getByText } = render(
        <TestWrapper>
          <ThemeToggle variant="card" />
        </TestWrapper>
      );

      expect(getByText('settings.appearance')).toBeTruthy();
    });
  });

  describe('Theme Switching', () => {
    it('should toggle from light to dark mode', async () => {
      const onToggle = jest.fn();
      const { getByRole } = render(
        <TestWrapper initialTheme="light">
          <ThemeToggle onToggle={onToggle} />
        </TestWrapper>
      );

      const toggleButton = getByRole('button');
      fireEvent.press(toggleButton);

      await waitFor(() => {
        expect(onToggle).toHaveBeenCalledWith(true);
      });
    });

    it('should toggle from dark to light mode', async () => {
      const onToggle = jest.fn();
      const { getByRole } = render(
        <TestWrapper initialTheme="dark">
          <ThemeToggle onToggle={onToggle} />
        </TestWrapper>
      );

      const toggleButton = getByRole('button');
      fireEvent.press(toggleButton);

      await waitFor(() => {
        expect(onToggle).toHaveBeenCalledWith(false);
      });
    });

    it('should persist theme preference', async () => {
      const { getByRole } = render(
        <TestWrapper>
          <ThemeToggle />
        </TestWrapper>
      );

      const toggleButton = getByRole('button');
      fireEvent.press(toggleButton);

      await waitFor(() => {
        expect(MockedAsyncStorageService.setItem).toHaveBeenCalledWith(
          'app_theme_mode',
          'dark'
        );
      });
    });

    it('should track analytics when theme changes', async () => {
      const { getByRole } = render(
        <TestWrapper>
          <ThemeToggle />
        </TestWrapper>
      );

      const toggleButton = getByRole('button');
      fireEvent.press(toggleButton);

      await waitFor(() => {
        expect(MockedAnalyticsService.trackEvent).toHaveBeenCalledWith(
          'theme_changed',
          expect.objectContaining({
            from_theme: 'light',
            to_theme: 'dark',
            method: 'manual',
          })
        );
      });
    });
  });

  describe('Disabled State', () => {
    it('should not respond to press when disabled', () => {
      const onToggle = jest.fn();
      const { getByRole } = render(
        <TestWrapper>
          <ThemeToggle disabled onToggle={onToggle} />
        </TestWrapper>
      );

      const toggleButton = getByRole('button');
      fireEvent.press(toggleButton);

      expect(onToggle).not.toHaveBeenCalled();
    });

    it('should have disabled styling when disabled', () => {
      const { getByRole } = render(
        <TestWrapper>
          <ThemeToggle disabled />
        </TestWrapper>
      );

      const toggleButton = getByRole('button');
      expect(toggleButton.props.style).toEqual(
        expect.objectContaining({
          opacity: 0.5,
        })
      );
    });
  });

  describe('RTL Support', () => {
    it('should render correctly in RTL mode', () => {
      jest.doMock('react-i18next', () => ({
        useTranslation: () => ({
          t: (key: string) => key,
          i18n: { language: 'ar' },
        }),
      }));

      const { getByText } = render(
        <TestWrapper>
          <ThemeToggle />
        </TestWrapper>
      );

      expect(getByText('settings.theme')).toBeTruthy();
    });
  });

  describe('Accessibility', () => {
    it('should have proper accessibility props', () => {
      const { getByRole } = render(
        <TestWrapper>
          <ThemeToggle />
        </TestWrapper>
      );

      const toggleButton = getByRole('button');
      expect(toggleButton).toBeTruthy();
    });

    it('should be accessible when disabled', () => {
      const { getByRole } = render(
        <TestWrapper>
          <ThemeToggle disabled />
        </TestWrapper>
      );

      const toggleButton = getByRole('button');
      expect(toggleButton.props.accessibilityState).toEqual(
        expect.objectContaining({
          disabled: true,
        })
      );
    });
  });

  describe('Animation', () => {
    it('should animate when theme changes', async () => {
      const { getByRole } = render(
        <TestWrapper>
          <ThemeToggle />
        </TestWrapper>
      );

      const toggleButton = getByRole('button');
      fireEvent.press(toggleButton);

      // Animation should complete within reasonable time
      await waitFor(() => {
        expect(MockedAnalyticsService.trackEvent).toHaveBeenCalled();
      }, { timeout: 1000 });
    });
  });

  describe('Error Handling', () => {
    it('should handle storage errors gracefully', async () => {
      MockedAsyncStorageService.setItem.mockRejectedValue(new Error('Storage error'));

      const { getByRole } = render(
        <TestWrapper>
          <ThemeToggle />
        </TestWrapper>
      );

      const toggleButton = getByRole('button');
      fireEvent.press(toggleButton);

      // Should not crash and should still attempt to track analytics
      await waitFor(() => {
        expect(MockedAnalyticsService.trackEvent).toHaveBeenCalled();
      });
    });

    it('should handle analytics errors gracefully', async () => {
      MockedAnalyticsService.trackEvent.mockRejectedValue(new Error('Analytics error'));

      const { getByRole } = render(
        <TestWrapper>
          <ThemeToggle />
        </TestWrapper>
      );

      const toggleButton = getByRole('button');
      fireEvent.press(toggleButton);

      // Should not crash
      await waitFor(() => {
        expect(MockedAsyncStorageService.setItem).toHaveBeenCalled();
      });
    });
  });
});
