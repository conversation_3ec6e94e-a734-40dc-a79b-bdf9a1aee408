import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  Animated,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/Button';
import { LoadingOverlay, ProgressBar } from '@/components/ui/LoadingStates';
import { ErrorMessage } from '@/components/ui/ErrorBoundary';
import { GeminiService } from '@/services/api/geminiService';
import { COLORS, SPACING, BORDER_RADIUS, TYPOGRAPHY, QUIZ_CONFIG, QUESTION_TYPES, MATERIAL_SCOPES } from '@/constants';
import { QuizGenerationParams } from '@/types';

interface EnhancedQuizSetupProps {
  file: { uri: string; type: 'pdf' | 'image'; name: string };
  onQuizGenerated: (quiz: any) => void;
  onCancel: () => void;
}

export const EnhancedQuizSetup: React.FC<EnhancedQuizSetupProps> = ({
  file,
  onQuizGenerated,
  onCancel,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { language } = useLanguage();
  
  const [subject, setSubject] = useState('');
  const [questionType, setQuestionType] = useState<'multiple_choice' | 'true_false' | 'explain_justify' | 'mix'>('multiple_choice');
  const [numberOfQuestions, setNumberOfQuestions] = useState(QUIZ_CONFIG.DEFAULT_QUESTIONS);
  const [timeLimit, setTimeLimit] = useState<number | null>(QUIZ_CONFIG.DEFAULT_TIME_LIMIT);
  const [focusAreas, setFocusAreas] = useState('');
  const [materialScope, setMaterialScope] = useState('single_chapter');
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [error, setError] = useState<string | null>(null);

  const fadeAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleGenerateQuiz = async () => {
    if (!subject.trim()) {
      Alert.alert(t('common.error') || 'Error', 'Please enter a subject name');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setProgress(0);

      // Step 1: Extract text from file
      setCurrentStep('Extracting text from file...');
      setProgress(0.2);
      const extractedText = await GeminiService.extractTextFromFile(file.uri, file.type);

      // Step 2: Generate quiz
      setCurrentStep('Generating quiz questions...');
      setProgress(0.6);
      
      const params: QuizGenerationParams = {
        subject,
        questionType,
        numberOfQuestions,
        focusAreas: focusAreas || undefined,
        materialScope,
      };

      const questions = await GeminiService.generateQuiz(params, extractedText);

      // Step 3: Finalize
      setCurrentStep('Finalizing quiz...');
      setProgress(1);

      const quiz = {
        id: `quiz_${Date.now()}`,
        title: `${subject} Quiz`,
        subject,
        questions,
        timeLimit,
        createdAt: new Date(),
        sourceFile: file.name,
        extractedText,
      };

      setTimeout(() => {
        onQuizGenerated(quiz);
      }, 500);

    } catch (error: any) {
      console.error('Error generating quiz:', error);
      setError(error.message || 'Failed to generate quiz');
    } finally {
      setLoading(false);
      setProgress(0);
      setCurrentStep('');
    }
  };

  const renderQuestionTypeCard = (type: typeof questionType, icon: string, title: string, description: string) => {
    const isSelected = questionType === type;
    
    return (
      <TouchableOpacity
        key={type}
        style={[
          styles.optionCard,
          {
            backgroundColor: isSelected ? theme.colors.primary : theme.colors.surface,
            borderColor: isSelected ? theme.colors.primary : theme.colors.border,
          }
        ]}
        onPress={() => setQuestionType(type)}
      >
        <View style={styles.optionHeader}>
          <Ionicons 
            name={icon as any} 
            size={24} 
            color={isSelected ? theme.colors.textOnPrimary : theme.colors.primary} 
          />
          <Text style={[
            styles.optionTitle,
            { color: isSelected ? theme.colors.textOnPrimary : theme.colors.text }
          ]}>
            {title}
          </Text>
        </View>
        <Text style={[
          styles.optionDescription,
          { color: isSelected ? theme.colors.textOnPrimary : theme.colors.textSecondary }
        ]}>
          {description}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderMaterialScopeCard = (scope: string, title: string, description: string) => {
    const isSelected = materialScope === scope;
    
    return (
      <TouchableOpacity
        key={scope}
        style={[
          styles.scopeCard,
          {
            backgroundColor: isSelected ? theme.colors.primaryLight : theme.colors.surface,
            borderColor: isSelected ? theme.colors.primary : theme.colors.border,
          }
        ]}
        onPress={() => setMaterialScope(scope)}
      >
        <Text style={[
          styles.scopeTitle,
          { color: isSelected ? theme.colors.textOnPrimary : theme.colors.text }
        ]}>
          {title}
        </Text>
        <Text style={[
          styles.scopeDescription,
          { color: isSelected ? theme.colors.textOnPrimary : theme.colors.textSecondary }
        ]}>
          {description}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <LoadingOverlay 
        visible={loading} 
        message={currentStep}
      >
        {loading && (
          <View style={styles.progressContainer}>
            <ProgressBar progress={progress} height={6} />
            <Text style={[styles.progressText, { color: theme.colors.textSecondary }]}>
              {Math.round(progress * 100)}%
            </Text>
          </View>
        )}
      </LoadingOverlay>

      <Animated.ScrollView 
        style={[styles.scrollView, { opacity: fadeAnim }]}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <LinearGradient
          colors={[theme.colors.primary, theme.colors.primaryDark]}
          style={styles.header}
        >
          <Text style={[styles.headerTitle, { color: theme.colors.textOnPrimary }]}>
            {t('quiz.setupTitle') || 'Quiz Setup'}
          </Text>
          <Text style={[styles.headerSubtitle, { color: theme.colors.textOnPrimary }]}>
            {file.name}
          </Text>
        </LinearGradient>

        <View style={styles.content}>
          {/* Error Message */}
          {error && (
            <ErrorMessage
              message={error}
              type="error"
              onRetry={() => setError(null)}
              visible={!!error}
            />
          )}

          {/* Subject Input */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              {t('quiz.subject') || 'Subject'}
            </Text>
            <TextInput
              style={[
                styles.textInput,
                {
                  backgroundColor: theme.colors.inputBackground,
                  borderColor: theme.colors.border,
                  color: theme.colors.text,
                }
              ]}
              value={subject}
              onChangeText={setSubject}
              placeholder={t('quiz.subjectPlaceholder') || 'Enter subject name'}
              placeholderTextColor={theme.colors.textTertiary}
            />
          </View>

          {/* Question Type Selection */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              {t('quiz.questionType') || 'Question Type'}
            </Text>
            <View style={styles.optionsGrid}>
              {renderQuestionTypeCard(
                'multiple_choice',
                'radio-button-on-outline',
                language === 'ar' ? 'اختيار من متعدد' : 'Multiple Choice',
                language === 'ar' ? 'أسئلة بخيارات متعددة' : 'Questions with multiple options'
              )}
              {renderQuestionTypeCard(
                'true_false',
                'checkmark-circle-outline',
                language === 'ar' ? 'صح / خطأ' : 'True/False',
                language === 'ar' ? 'أسئلة صح أو خطأ' : 'True or false questions'
              )}
              {renderQuestionTypeCard(
                'explain_justify',
                'create-outline',
                language === 'ar' ? 'علل / اشرح' : 'Explain/Justify',
                language === 'ar' ? 'أسئلة تتطلب شرح' : 'Questions requiring explanation'
              )}
              {renderQuestionTypeCard(
                'mix',
                'shuffle-outline',
                language === 'ar' ? 'مزيج من كل الأنواع' : 'Mix (All Types)',
                language === 'ar' ? 'خليط من جميع الأنواع' : 'Mix of all question types'
              )}
            </View>
          </View>

          {/* Number of Questions */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              {t('quiz.numberOfQuestions') || 'Number of Questions'}: {numberOfQuestions}
            </Text>
            <View style={styles.sliderContainer}>
              <TouchableOpacity
                style={[styles.sliderButton, { backgroundColor: theme.colors.primary }]}
                onPress={() => setNumberOfQuestions(Math.max(QUIZ_CONFIG.MIN_QUESTIONS, numberOfQuestions - 1))}
              >
                <Ionicons name="remove" size={20} color={theme.colors.textOnPrimary} />
              </TouchableOpacity>
              <View style={[styles.sliderValue, { backgroundColor: theme.colors.surface }]}>
                <Text style={[styles.sliderValueText, { color: theme.colors.text }]}>
                  {numberOfQuestions}
                </Text>
              </View>
              <TouchableOpacity
                style={[styles.sliderButton, { backgroundColor: theme.colors.primary }]}
                onPress={() => setNumberOfQuestions(Math.min(QUIZ_CONFIG.MAX_QUESTIONS, numberOfQuestions + 1))}
              >
                <Ionicons name="add" size={20} color={theme.colors.textOnPrimary} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Material Scope */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              {t('quiz.materialScope') || 'Material Scope'}
            </Text>
            <View style={styles.scopeGrid}>
              {MATERIAL_SCOPES.map(scope => 
                renderMaterialScopeCard(
                  scope.value,
                  scope.label[language] || scope.label.en,
                  scope.value === 'single_chapter' ? 'Focus on one chapter' : 
                  scope.value === 'full_semester' ? 'Cover full semester' :
                  scope.value === 'book' ? 'Entire book coverage' : 'Exam preparation'
                )
              )}
            </View>
          </View>

          {/* Focus Areas */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              {t('quiz.focusAreas') || 'Focus Areas'} ({t('common.optional') || 'Optional'})
            </Text>
            <TextInput
              style={[
                styles.textArea,
                {
                  backgroundColor: theme.colors.inputBackground,
                  borderColor: theme.colors.border,
                  color: theme.colors.text,
                }
              ]}
              value={focusAreas}
              onChangeText={setFocusAreas}
              placeholder={t('quiz.focusAreasPlaceholder') || 'Specific topics or areas to focus on...'}
              placeholderTextColor={theme.colors.textTertiary}
              multiline
              numberOfLines={3}
            />
          </View>

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <Button
              title={t('common.cancel') || 'Cancel'}
              onPress={onCancel}
              variant="outline"
              style={styles.cancelButton}
            />
            <Button
              title={t('quiz.generateQuiz') || 'Generate Quiz'}
              onPress={handleGenerateQuiz}
              loading={loading}
              style={styles.generateButton}
            />
          </View>
        </View>
      </Animated.ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: SPACING['3xl'],
    paddingTop: SPACING['5xl'],
    borderBottomLeftRadius: BORDER_RADIUS['2xl'],
    borderBottomRightRadius: BORDER_RADIUS['2xl'],
  },
  headerTitle: {
    fontSize: TYPOGRAPHY.fontSize['3xl'],
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  headerSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.base,
    textAlign: 'center',
    opacity: 0.9,
  },
  content: {
    padding: SPACING.xl,
  },
  section: {
    marginBottom: SPACING['3xl'],
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: '600',
    marginBottom: SPACING.md,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    fontSize: TYPOGRAPHY.fontSize.base,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    fontSize: TYPOGRAPHY.fontSize.base,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  optionsGrid: {
    gap: SPACING.md,
  },
  optionCard: {
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 2,
  },
  optionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  optionTitle: {
    fontSize: TYPOGRAPHY.fontSize.base,
    fontWeight: '600',
    marginLeft: SPACING.md,
  },
  optionDescription: {
    fontSize: TYPOGRAPHY.fontSize.sm,
  },
  scopeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.md,
  },
  scopeCard: {
    flex: 1,
    minWidth: '45%',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
  },
  scopeTitle: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
    marginBottom: SPACING.xs,
  },
  scopeDescription: {
    fontSize: TYPOGRAPHY.fontSize.xs,
  },
  sliderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.lg,
  },
  sliderButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sliderValue: {
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    minWidth: 80,
    alignItems: 'center',
  },
  sliderValueText: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: '600',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: SPACING.md,
    marginTop: SPACING.xl,
  },
  cancelButton: {
    flex: 1,
  },
  generateButton: {
    flex: 2,
  },
  progressContainer: {
    marginTop: SPACING.lg,
    width: '100%',
  },
  progressText: {
    textAlign: 'center',
    marginTop: SPACING.sm,
    fontSize: TYPOGRAPHY.fontSize.sm,
  },
});
