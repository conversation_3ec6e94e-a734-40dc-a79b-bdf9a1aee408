import { AsyncStorageService } from '../storage/asyncStorage';

// Cache Entry Interface
interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  expiresAt: number;
  accessCount: number;
  lastAccessed: number;
  tags?: string[];
  size?: number;
}

// Cache Configuration
interface CacheConfig {
  defaultTTL: number; // Time to live in milliseconds
  maxSize: number; // Maximum cache size in bytes
  maxEntries: number; // Maximum number of entries
  cleanupInterval: number; // Cleanup interval in milliseconds
}

// Cache Statistics
interface CacheStats {
  hits: number;
  misses: number;
  entries: number;
  totalSize: number;
  hitRate: number;
  oldestEntry: number;
  newestEntry: number;
}

// Advanced Cache Service
export class CacheService {
  private static cache = new Map<string, CacheEntry>();
  private static stats = {
    hits: 0,
    misses: 0,
  };
  
  private static config: CacheConfig = {
    defaultTTL: 30 * 60 * 1000, // 30 minutes
    maxSize: 50 * 1024 * 1024, // 50MB
    maxEntries: 1000,
    cleanupInterval: 5 * 60 * 1000, // 5 minutes
  };

  private static cleanupTimer: NodeJS.Timeout | null = null;

  // Initialize cache service
  static async initialize(config?: Partial<CacheConfig>): Promise<void> {
    if (config) {
      this.config = { ...this.config, ...config };
    }

    await this.loadFromStorage();
    this.startCleanupTimer();
  }

  // Set cache entry
  static async set<T>(
    key: string,
    data: T,
    ttl?: number,
    tags?: string[]
  ): Promise<void> {
    const now = Date.now();
    const expiresAt = now + (ttl || this.config.defaultTTL);
    const size = this.calculateSize(data);

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt,
      accessCount: 0,
      lastAccessed: now,
      tags,
      size,
    };

    // Check if adding this entry would exceed limits
    if (this.cache.size >= this.config.maxEntries) {
      await this.evictLeastRecentlyUsed();
    }

    const currentSize = this.getTotalSize();
    if (currentSize + size > this.config.maxSize) {
      await this.evictBySize(size);
    }

    this.cache.set(key, entry);
    await this.persistToStorage(key, entry);
  }

  // Get cache entry
  static async get<T>(key: string): Promise<T | null> {
    const entry = this.cache.get(key);

    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // Check if expired
    if (Date.now() > entry.expiresAt) {
      await this.delete(key);
      this.stats.misses++;
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    this.stats.hits++;

    return entry.data as T;
  }

  // Delete cache entry
  static async delete(key: string): Promise<boolean> {
    const deleted = this.cache.delete(key);
    if (deleted) {
      await this.removeFromStorage(key);
    }
    return deleted;
  }

  // Check if key exists and is not expired
  static has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (Date.now() > entry.expiresAt) {
      this.delete(key);
      return false;
    }
    
    return true;
  }

  // Clear all cache entries
  static async clear(): Promise<void> {
    this.cache.clear();
    this.stats.hits = 0;
    this.stats.misses = 0;
    await this.clearStorage();
  }

  // Clear cache entries by tag
  static async clearByTag(tag: string): Promise<number> {
    let cleared = 0;
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags?.includes(tag)) {
        keysToDelete.push(key);
      }
    }

    for (const key of keysToDelete) {
      await this.delete(key);
      cleared++;
    }

    return cleared;
  }

  // Get cache statistics
  static getStats(): CacheStats {
    const entries = Array.from(this.cache.values());
    const totalSize = this.getTotalSize();
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0;

    const timestamps = entries.map(e => e.timestamp);
    const oldestEntry = timestamps.length > 0 ? Math.min(...timestamps) : 0;
    const newestEntry = timestamps.length > 0 ? Math.max(...timestamps) : 0;

    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      entries: this.cache.size,
      totalSize,
      hitRate,
      oldestEntry,
      newestEntry,
    };
  }

  // Get cache entry info
  static getEntryInfo(key: string): Partial<CacheEntry> | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    return {
      timestamp: entry.timestamp,
      expiresAt: entry.expiresAt,
      accessCount: entry.accessCount,
      lastAccessed: entry.lastAccessed,
      tags: entry.tags,
      size: entry.size,
    };
  }

  // Memoization wrapper
  static memoize<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    keyGenerator?: (...args: Parameters<T>) => string,
    ttl?: number,
    tags?: string[]
  ): T {
    return (async (...args: Parameters<T>) => {
      const key = keyGenerator ? keyGenerator(...args) : `memoized_${fn.name}_${JSON.stringify(args)}`;
      
      // Try to get from cache first
      const cached = await this.get(key);
      if (cached !== null) {
        return cached;
      }

      // Execute function and cache result
      const result = await fn(...args);
      await this.set(key, result, ttl, tags);
      
      return result;
    }) as T;
  }

  // Batch operations
  static async setMany<T>(entries: Array<{ key: string; data: T; ttl?: number; tags?: string[] }>): Promise<void> {
    const promises = entries.map(({ key, data, ttl, tags }) => this.set(key, data, ttl, tags));
    await Promise.all(promises);
  }

  static async getMany<T>(keys: string[]): Promise<Record<string, T | null>> {
    const promises = keys.map(async key => ({ key, value: await this.get<T>(key) }));
    const results = await Promise.all(promises);
    
    return results.reduce((acc, { key, value }) => {
      acc[key] = value;
      return acc;
    }, {} as Record<string, T | null>);
  }

  // Cache warming
  static async warmCache<T>(
    keys: string[],
    dataLoader: (key: string) => Promise<T>,
    ttl?: number,
    tags?: string[]
  ): Promise<void> {
    const promises = keys.map(async key => {
      if (!this.has(key)) {
        try {
          const data = await dataLoader(key);
          await this.set(key, data, ttl, tags);
        } catch (error) {
          console.warn(`Failed to warm cache for key ${key}:`, error);
        }
      }
    });

    await Promise.all(promises);
  }

  // Private helper methods
  private static calculateSize(data: any): number {
    try {
      return new Blob([JSON.stringify(data)]).size;
    } catch {
      // Fallback estimation
      return JSON.stringify(data).length * 2; // Rough estimate for UTF-16
    }
  }

  private static getTotalSize(): number {
    return Array.from(this.cache.values()).reduce((total, entry) => total + (entry.size || 0), 0);
  }

  private static async evictLeastRecentlyUsed(): Promise<void> {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      await this.delete(oldestKey);
    }
  }

  private static async evictBySize(requiredSize: number): Promise<void> {
    const entries = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

    let freedSize = 0;
    for (const [key, entry] of entries) {
      await this.delete(key);
      freedSize += entry.size || 0;
      
      if (freedSize >= requiredSize) {
        break;
      }
    }
  }

  private static startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  private static async cleanup(): Promise<void> {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        keysToDelete.push(key);
      }
    }

    for (const key of keysToDelete) {
      await this.delete(key);
    }
  }

  // Storage persistence methods
  private static async loadFromStorage(): Promise<void> {
    try {
      const cacheData = await AsyncStorageService.getItem('cache_data');
      if (cacheData) {
        const parsed = JSON.parse(cacheData);
        this.cache = new Map(parsed.entries);
        this.stats = parsed.stats || { hits: 0, misses: 0 };
      }
    } catch (error) {
      console.error('Failed to load cache from storage:', error);
    }
  }

  private static async persistToStorage(key: string, entry: CacheEntry): Promise<void> {
    // For performance, we could implement a batched write strategy
    // For now, we'll persist the entire cache periodically
  }

  private static async removeFromStorage(key: string): Promise<void> {
    // Implementation would depend on storage strategy
  }

  private static async clearStorage(): Promise<void> {
    try {
      await AsyncStorageService.removeItem('cache_data');
    } catch (error) {
      console.error('Failed to clear cache storage:', error);
    }
  }

  // Shutdown cleanup
  static shutdown(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }
}
