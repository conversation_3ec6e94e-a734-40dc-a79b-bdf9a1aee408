import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { StatusBar } from 'expo-status-bar';
import { Animated } from 'react-native';
import { AsyncStorageService } from '@/services/storage/asyncStorage';
import { AnalyticsService } from '@/services/analytics/analyticsService';
import { ErrorHandler } from '@/services/error/errorHandler';
import { 
  Theme, 
  ThemeMode, 
  lightTheme, 
  darkTheme, 
  getTheme, 
  getStatusBarStyle,
  THEME_TRANSITION_DURATION 
} from '@/constants/themes';

// Theme Context Interface
interface ThemeContextType {
  theme: Theme;
  themeMode: ThemeMode;
  isDarkMode: boolean;
  toggleTheme: () => Promise<void>;
  setTheme: (mode: ThemeMode) => Promise<void>;
  isLoading: boolean;
  transitionValue: Animated.Value;
}

// Create Context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme Provider Props
interface ThemeProviderProps {
  children: ReactNode;
  initialTheme?: ThemeMode;
}

// Storage Key
const THEME_STORAGE_KEY = 'app_theme_mode';

// Theme Provider Component
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ 
  children, 
  initialTheme = 'light' 
}) => {
  const [themeMode, setThemeMode] = useState<ThemeMode>(initialTheme);
  const [isLoading, setIsLoading] = useState(true);
  const [transitionValue] = useState(new Animated.Value(1));

  // Get current theme object
  const theme = getTheme(themeMode);
  const isDarkMode = themeMode === 'dark';

  // Load theme preference on app start
  useEffect(() => {
    loadThemePreference();
  }, []);

  // Load theme preference from storage
  const loadThemePreference = async () => {
    try {
      setIsLoading(true);
      const storedTheme = await AsyncStorageService.getItem(THEME_STORAGE_KEY);
      
      if (storedTheme && (storedTheme === 'light' || storedTheme === 'dark')) {
        setThemeMode(storedTheme as ThemeMode);
        
        // Track theme load
        await AnalyticsService.trackEvent('theme_loaded', {
          theme: storedTheme,
          source: 'storage',
        });
      } else {
        // Use system preference or default to light
        const systemTheme = getSystemTheme();
        setThemeMode(systemTheme);
        
        // Save default theme
        await AsyncStorageService.setItem(THEME_STORAGE_KEY, systemTheme);
        
        // Track default theme set
        await AnalyticsService.trackEvent('theme_loaded', {
          theme: systemTheme,
          source: 'default',
        });
      }
    } catch (error) {
      await ErrorHandler.handleError(error, {
        context: 'ThemeProvider.loadThemePreference',
      }, false);
      
      // Fallback to light theme
      setThemeMode('light');
    } finally {
      setIsLoading(false);
    }
  };

  // Get system theme preference (simplified - in production you might use react-native-appearance)
  const getSystemTheme = (): ThemeMode => {
    // For now, default to light. In production, you could use:
    // import { Appearance } from 'react-native';
    // return Appearance.getColorScheme() === 'dark' ? 'dark' : 'light';
    return 'light';
  };

  // Save theme preference to storage
  const saveThemePreference = async (mode: ThemeMode) => {
    try {
      await AsyncStorageService.setItem(THEME_STORAGE_KEY, mode);
    } catch (error) {
      await ErrorHandler.handleError(error, {
        context: 'ThemeProvider.saveThemePreference',
        mode,
      }, false);
    }
  };

  // Animate theme transition
  const animateThemeTransition = () => {
    return new Promise<void>((resolve) => {
      // Fade out
      Animated.timing(transitionValue, {
        toValue: 0,
        duration: THEME_TRANSITION_DURATION / 2,
        useNativeDriver: true,
      }).start(() => {
        // Fade in with new theme
        Animated.timing(transitionValue, {
          toValue: 1,
          duration: THEME_TRANSITION_DURATION / 2,
          useNativeDriver: true,
        }).start(() => {
          resolve();
        });
      });
    });
  };

  // Set theme with animation and persistence
  const setTheme = async (mode: ThemeMode) => {
    if (mode === themeMode) return;

    try {
      // Start transition animation
      const animationPromise = animateThemeTransition();
      
      // Update theme mode
      setThemeMode(mode);
      
      // Save to storage
      await saveThemePreference(mode);
      
      // Track theme change
      await AnalyticsService.trackEvent('theme_changed', {
        from_theme: themeMode,
        to_theme: mode,
        method: 'manual',
        timestamp: new Date().toISOString(),
      });
      
      // Wait for animation to complete
      await animationPromise;
      
    } catch (error) {
      await ErrorHandler.handleError(error, {
        context: 'ThemeProvider.setTheme',
        mode,
      });
    }
  };

  // Toggle between light and dark themes
  const toggleTheme = async () => {
    const newMode: ThemeMode = themeMode === 'light' ? 'dark' : 'light';
    await setTheme(newMode);
  };

  // Context value
  const contextValue: ThemeContextType = {
    theme,
    themeMode,
    isDarkMode,
    toggleTheme,
    setTheme,
    isLoading,
    transitionValue,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <StatusBar style={getStatusBarStyle(theme)} />
      <Animated.View 
        style={{ 
          flex: 1, 
          opacity: transitionValue,
          backgroundColor: theme.colors.background 
        }}
      >
        {children}
      </Animated.View>
    </ThemeContext.Provider>
  );
};

// Custom hook to use theme context
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  
  return context;
};

// Hook for theme-aware styles
export const useThemedStyles = <T extends Record<string, any>>(
  styleFactory: (theme: Theme) => T
): T => {
  const { theme } = useTheme();
  return styleFactory(theme);
};

// Hook for theme colors
export const useThemeColors = () => {
  const { theme } = useTheme();
  return theme.colors;
};

// Hook for theme mode checking
export const useIsDarkMode = (): boolean => {
  const { isDarkMode } = useTheme();
  return isDarkMode;
};

// Theme transition wrapper component
interface ThemeTransitionProps {
  children: ReactNode;
  style?: any;
}

export const ThemeTransition: React.FC<ThemeTransitionProps> = ({ 
  children, 
  style 
}) => {
  const { transitionValue, theme } = useTheme();
  
  return (
    <Animated.View 
      style={[
        { 
          opacity: transitionValue,
          backgroundColor: theme.colors.background 
        }, 
        style
      ]}
    >
      {children}
    </Animated.View>
  );
};
