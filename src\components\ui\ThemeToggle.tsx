import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Easing,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import { Icon, ICON_SIZES } from './Icon';
import { useTheme, useThemedStyles } from '@/contexts/ThemeContext';
import { BounceView } from '@/components/animations/AnimatedComponents';
import { Theme } from '@/constants/themes';

// Component Props
interface ThemeToggleProps {
  variant?: 'default' | 'compact' | 'card';
  showLabel?: boolean;
  disabled?: boolean;
  onToggle?: (isDarkMode: boolean) => void;
}

// Animation Constants
const TOGGLE_ANIMATION_DURATION = 300;
const ICON_ROTATION_DURATION = 400;

export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  variant = 'default',
  showLabel = true,
  disabled = false,
  onToggle,
}) => {
  const { t, i18n } = useTranslation();
  const { theme, isDarkMode, toggleTheme, isLoading } = useTheme();
  const styles = useThemedStyles(createStyles);
  
  // Animation values
  const toggleAnimation = useRef(new Animated.Value(isDarkMode ? 1 : 0)).current;
  const iconRotation = useRef(new Animated.Value(0)).current;
  const scaleAnimation = useRef(new Animated.Value(1)).current;

  // Update animation when theme changes
  useEffect(() => {
    Animated.parallel([
      Animated.timing(toggleAnimation, {
        toValue: isDarkMode ? 1 : 0,
        duration: TOGGLE_ANIMATION_DURATION,
        easing: Easing.bezier(0.4, 0, 0.2, 1),
        useNativeDriver: false,
      }),
      Animated.timing(iconRotation, {
        toValue: isDarkMode ? 1 : 0,
        duration: ICON_ROTATION_DURATION,
        easing: Easing.elastic(1.2),
        useNativeDriver: true,
      }),
    ]).start();
  }, [isDarkMode]);

  // Handle toggle press
  const handleToggle = async () => {
    if (disabled || isLoading) return;

    // Animate press feedback
    Animated.sequence([
      Animated.timing(scaleAnimation, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnimation, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    // Toggle theme
    await toggleTheme();
    
    // Call optional callback
    onToggle?.(!isDarkMode);
  };

  // Animation interpolations
  const toggleTranslateX = toggleAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [2, 26],
  });

  const toggleBackgroundColor = toggleAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.border, theme.colors.primary],
  });

  const iconRotationDegrees = iconRotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // Render toggle switch
  const renderToggleSwitch = () => (
    <Animated.View 
      style={[
        styles.toggleContainer,
        { backgroundColor: toggleBackgroundColor },
        variant === 'compact' && styles.toggleContainerCompact,
      ]}
    >
      <Animated.View
        style={[
          styles.toggleThumb,
          {
            transform: [
              { translateX: toggleTranslateX },
              { scale: scaleAnimation },
            ],
          },
        ]}
      >
        <Animated.View
          style={{
            transform: [{ rotate: iconRotationDegrees }],
          }}
        >
          <Icon
            name={isDarkMode ? 'moon' : 'sun'}
            library="Feather"
            size={variant === 'compact' ? ICON_SIZES.sm : ICON_SIZES.md}
            color={isDarkMode ? theme.colors.primary : theme.colors.warning}
          />
        </Animated.View>
      </Animated.View>
    </Animated.View>
  );

  // Render labels
  const renderLabel = () => {
    if (!showLabel) return null;

    return (
      <View style={styles.labelContainer}>
        <Text style={styles.label}>
          {t('settings.theme')}
        </Text>
        <Text style={styles.sublabel}>
          {isDarkMode ? t('settings.darkMode') : t('settings.lightMode')}
        </Text>
      </View>
    );
  };

  // Render card variant
  if (variant === 'card') {
    return (
      <BounceView onPress={handleToggle} disabled={disabled || isLoading}>
        <LinearGradient
          colors={[theme.colors.cardBackground, theme.colors.surfaceSecondary]}
          style={styles.cardContainer}
        >
          <View style={styles.cardContent}>
            <View style={styles.cardIconContainer}>
              <Icon
                name="palette"
                library="MaterialCommunityIcons"
                size={ICON_SIZES.lg}
                color={theme.colors.primary}
              />
            </View>
            <View style={styles.cardTextContainer}>
              <Text style={styles.cardTitle}>{t('settings.appearance')}</Text>
              <Text style={styles.cardSubtitle}>
                {isDarkMode ? t('settings.darkMode') : t('settings.lightMode')}
              </Text>
            </View>
            {renderToggleSwitch()}
          </View>
        </LinearGradient>
      </BounceView>
    );
  }

  // Render default or compact variant
  return (
    <TouchableOpacity
      style={[
        styles.container,
        variant === 'compact' && styles.containerCompact,
        disabled && styles.containerDisabled,
      ]}
      onPress={handleToggle}
      disabled={disabled || isLoading}
      activeOpacity={0.7}
    >
      {i18n.language === 'ar' ? (
        // RTL layout
        <>
          {renderToggleSwitch()}
          {renderLabel()}
        </>
      ) : (
        // LTR layout
        <>
          {renderLabel()}
          {renderToggleSwitch()}
        </>
      )}
    </TouchableOpacity>
  );
};

// Styles factory
const createStyles = (theme: Theme) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
  },
  containerCompact: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
  },
  containerDisabled: {
    opacity: 0.5,
  },
  labelContainer: {
    flex: 1,
    marginRight: theme.spacing.lg,
  },
  label: {
    fontSize: theme.typography.fontSize.base,
    fontFamily: theme.typography.fontFamily.medium,
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  sublabel: {
    fontSize: theme.typography.fontSize.sm,
    fontFamily: theme.typography.fontFamily.regular,
    color: theme.colors.textSecondary,
  },
  toggleContainer: {
    width: 52,
    height: 28,
    borderRadius: 14,
    padding: 2,
    justifyContent: 'center',
  },
  toggleContainerCompact: {
    width: 44,
    height: 24,
    borderRadius: 12,
  },
  toggleThumb: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.cardBackground,
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.sm,
  },
  // Card variant styles
  cardContainer: {
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.lg,
    marginVertical: theme.spacing.sm,
    ...theme.shadows.md,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardIconContainer: {
    width: 48,
    height: 48,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.lg,
  },
  cardTextContainer: {
    flex: 1,
  },
  cardTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontFamily: theme.typography.fontFamily.semiBold,
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  cardSubtitle: {
    fontSize: theme.typography.fontSize.sm,
    fontFamily: theme.typography.fontFamily.regular,
    color: theme.colors.textSecondary,
  },
});
