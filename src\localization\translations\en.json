{"app": {"name": "Test Me", "tagline": "Smart Quiz Generation from Your Study Materials"}, "navigation": {"home": "Home", "quiz": "Quiz", "history": "History", "settings": "Settings", "chat": "AI Chat"}, "home": {"welcome": "Welcome to Test Me", "subtitle": "Upload your study materials and generate personalized quizzes", "uploadFile": "Upload File", "recentQuizzes": "Recent Quizzes", "noRecentQuizzes": "No recent quizzes found", "startQuiz": "Start Quiz", "quickActions": "Quick Actions", "studyTips": "Study Tips", "dailyStudyHabit": "Daily Study Habit", "studyTipText": "Consistent daily practice, even for 15 minutes, is more effective than long cramming sessions.", "viewAll": "View All", "tapToReview": "Tap to review", "totalQuizzes": "Total Quizzes", "avgScore": "Avg Score", "studyStreak": "Study Streak", "questions": "Questions"}, "fileUpload": {"title": "Upload Study Material", "selectFile": "Select File", "supportedFormats": "Supported formats: PDF, JPG, PNG", "maxSize": "Maximum file size: 10MB", "fileName": "File Name", "fileSize": "File Size", "remove": "Remove", "continue": "Continue"}, "quizSetup": {"title": "Quiz Setup", "subject": "Subject", "subjectPlaceholder": "Enter subject name", "questionType": "Question Type", "numberOfQuestions": "Number of Questions", "timeLimit": "Time Limit (minutes)", "noTimeLimit": "No Time Limit", "focusAreas": "Focus Areas (Optional)", "focusAreasPlaceholder": "Enter specific topics to focus on", "materialScope": "Material Scope", "generateQuiz": "Generate Quiz", "generating": "Generating Quiz..."}, "quiz": {"question": "Question {{current}} of {{total}}", "timeRemaining": "Time Remaining", "submit": "Submit Answer", "next": "Next", "previous": "Previous", "finish": "Finish Quiz", "confirmFinish": "Are you sure you want to finish the quiz?", "yes": "Yes", "no": "No", "loading": "Loading question...", "explanation": "Explanation", "source": "Source", "correct": "Correct", "incorrect": "Incorrect"}, "results": {"title": "Quiz Results", "score": "Your Score", "percentage": "{{score}}/{{total}} ({{percentage}}%)", "timeTaken": "Time Taken", "excellent": "Excellent!", "good": "Good Job!", "needsImprovement": "Needs Improvement", "reviewAnswers": "Review Answers", "retakeQuiz": "Retake Quiz", "backToHome": "Back to Home", "saveResults": "Save Results"}, "history": {"title": "Quiz History", "noHistory": "No quiz history found", "subject": "Subject", "date": "Date", "score": "Score", "viewDetails": "View Details", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this quiz?"}, "settings": {"title": "Settings", "language": "Language", "theme": "Theme", "appearance": "Appearance", "light": "Light", "dark": "Dark", "lightMode": "Light Mode", "darkMode": "Dark Mode", "notifications": "Notifications", "clearData": "Clear All Data", "confirmClearData": "This will delete all your quiz history and settings. Are you sure?", "about": "About", "version": "Version", "support": "Support", "apiKeys": "API Keys", "addApiKey": "Add API Key", "apiKeyName": "API Key Name", "apiKeyNamePlaceholder": "e.g., My Gemini Key", "apiKey": "API Key", "apiKeyPlaceholder": "AIza...", "model": "Model", "setDefault": "<PERSON>", "deleteApiKey": "Delete API Key", "confirmDeleteApiKey": "Are you sure you want to delete this API key?", "noApiKeys": "No API keys configured", "apiKeyAdded": "API key added successfully", "apiKeyDeleted": "API key deleted successfully", "invalidApiKey": "Invalid API key format", "apiKeyValidationFailed": "API key validation failed", "howToGetApiKey": "How to get a Gemini API Key:", "apiKeyInstructions": "1. Go to Google AI Studio (ai.google.dev)\n2. Sign in with your Google account\n3. Click \"Get API Key\"\n4. Create a new API key\n5. Copy and paste it here", "studyReminders": "Study Reminders", "quizCompletion": "Quiz Completion", "streakMilestones": "Streak Milestones", "reminderTime": "Reminder Time", "dataManagement": "Data Management", "notificationSettings": "Notification Settings"}, "chat": {"title": "AI Assistant", "placeholder": "Ask me anything about your studies...", "send": "Send", "thinking": "AI is thinking...", "error": "Sorry, I couldn't process your request. Please try again.", "welcomeTitle": "Welcome to your AI Study Assistant!", "welcomeMessage": "I'm here to help you with your studies. Ask me questions, get explanations, or use the quick actions below.", "quickActions": "Quick Actions", "exportChat": "Export <PERSON>", "clearChat": "Clear Chat", "confirmClearChat": "Are you sure you want to clear this conversation?", "chatExported": "Chat exported successfully", "noApiKey": "No API key configured. Please add an API key in Settings.", "retryMessage": "Retry", "messageTypes": {"text": "General", "quiz": "Quiz Help", "study": "Study Help", "error": "Error"}, "suggestions": {"explainTopic": "Explain this topic", "createQuestions": "Create practice questions", "studyTips": "Study tips", "summarize": "Summarize content", "memoryTechniques": "Memory techniques", "motivation": "Study motivation"}}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "ok": "OK", "save": "Save", "delete": "Delete", "edit": "Edit", "retry": "Retry", "close": "Close"}, "errors": {"fileUpload": "Failed to upload file", "quizGeneration": "Failed to generate quiz", "networkError": "Network error. Please check your connection.", "unknownError": "An unknown error occurred"}}