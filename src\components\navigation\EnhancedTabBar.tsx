import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { COLORS, SPACING, BORDER_RADIUS, TYPOGRAPHY } from '@/constants';
import { OfflineManager } from '@/services/offline/OfflineManager';

const { width: screenWidth } = Dimensions.get('window');

interface TabBarProps {
  state: any;
  descriptors: any;
  navigation: any;
}

export const EnhancedTabBar: React.FC<TabBarProps> = ({
  state,
  descriptors,
  navigation,
}) => {
  const { theme } = useTheme();
  const { language } = useLanguage();
  const [isOnline, setIsOnline] = React.useState(true);
  const [pendingActions, setPendingActions] = React.useState(0);

  const slideAnim = React.useRef(new Animated.Value(0)).current;
  const scaleAnim = React.useRef(new Animated.Value(1)).current;

  React.useEffect(() => {
    // Listen for network status changes
    const unsubscribe = OfflineManager.addNetworkListener((online) => {
      setIsOnline(online);
      
      if (!online) {
        // Show offline indicator animation
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.1,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();
      }
    });

    // Update pending actions count
    const updatePendingActions = () => {
      setPendingActions(OfflineManager.getPendingActionsCount());
    };

    const interval = setInterval(updatePendingActions, 5000);
    updatePendingActions();

    return () => {
      unsubscribe();
      clearInterval(interval);
    };
  }, []);

  const getTabConfig = (routeName: string) => {
    const configs = {
      Home: {
        icon: 'home',
        activeIcon: 'home',
        label: { en: 'Home', ar: 'الرئيسية' },
      },
      QuizSetup: {
        icon: 'add-circle-outline',
        activeIcon: 'add-circle',
        label: { en: 'Create', ar: 'إنشاء' },
      },
      QuizHistory: {
        icon: 'library-outline',
        activeIcon: 'library',
        label: { en: 'History', ar: 'السجل' },
      },
      Chat: {
        icon: 'chatbubble-outline',
        activeIcon: 'chatbubble',
        label: { en: 'AI Chat', ar: 'المساعد الذكي' },
      },
      Settings: {
        icon: 'settings-outline',
        activeIcon: 'settings',
        label: { en: 'Settings', ar: 'الإعدادات' },
      },
    };

    return configs[routeName as keyof typeof configs] || {
      icon: 'ellipse-outline',
      activeIcon: 'ellipse',
      label: { en: routeName, ar: routeName },
    };
  };

  const renderTab = (route: any, index: number) => {
    const { options } = descriptors[route.key];
    const isFocused = state.index === index;
    const config = getTabConfig(route.name);

    const onPress = () => {
      const event = navigation.emit({
        type: 'tabPress',
        target: route.key,
        canPreventDefault: true,
      });

      if (!isFocused && !event.defaultPrevented) {
        navigation.navigate(route.name);
      }
    };

    const onLongPress = () => {
      navigation.emit({
        type: 'tabLongPress',
        target: route.key,
      });
    };

    const animatedStyle = {
      transform: [
        {
          scale: isFocused ? scaleAnim : 1,
        },
      ],
    };

    return (
      <TouchableOpacity
        key={route.key}
        accessibilityRole="button"
        accessibilityState={isFocused ? { selected: true } : {}}
        accessibilityLabel={options.tabBarAccessibilityLabel}
        testID={options.tabBarTestID}
        onPress={onPress}
        onLongPress={onLongPress}
        style={styles.tabButton}
      >
        <Animated.View style={[styles.tabContent, animatedStyle]}>
          {isFocused && (
            <LinearGradient
              colors={[theme.colors.primary, theme.colors.primaryDark]}
              style={styles.activeTabBackground}
            />
          )}
          
          <View style={styles.tabIconContainer}>
            <Ionicons
              name={isFocused ? config.activeIcon : config.icon}
              size={24}
              color={isFocused ? theme.colors.textOnPrimary : theme.colors.textSecondary}
            />
            
            {/* Notification badge for Chat tab */}
            {route.name === 'Chat' && pendingActions > 0 && (
              <View style={[styles.badge, { backgroundColor: theme.colors.error }]}>
                <Text style={[styles.badgeText, { color: theme.colors.textOnPrimary }]}>
                  {pendingActions > 99 ? '99+' : pendingActions}
                </Text>
              </View>
            )}
          </View>
          
          <Text
            style={[
              styles.tabLabel,
              {
                color: isFocused ? theme.colors.textOnPrimary : theme.colors.textSecondary,
                fontSize: isFocused ? TYPOGRAPHY.fontSize.xs + 1 : TYPOGRAPHY.fontSize.xs,
              },
            ]}
          >
            {config.label[language] || config.label.en}
          </Text>
        </Animated.View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
      {/* Offline indicator */}
      {!isOnline && (
        <Animated.View
          style={[
            styles.offlineIndicator,
            { backgroundColor: theme.colors.warning, transform: [{ scale: scaleAnim }] },
          ]}
        >
          <Ionicons name="cloud-offline" size={16} color={theme.colors.textOnPrimary} />
          <Text style={[styles.offlineText, { color: theme.colors.textOnPrimary }]}>
            {language === 'ar' ? 'غير متصل' : 'Offline'}
          </Text>
        </Animated.View>
      )}

      {/* Tab bar */}
      <LinearGradient
        colors={[theme.colors.surface, theme.colors.surfaceElevated]}
        style={styles.tabBar}
      >
        <View style={styles.tabContainer}>
          {state.routes.map((route: any, index: number) => renderTab(route, index))}
        </View>
      </LinearGradient>

      {/* Active tab indicator */}
      <Animated.View
        style={[
          styles.activeIndicator,
          {
            backgroundColor: theme.colors.primary,
            left: (screenWidth / state.routes.length) * state.index + (screenWidth / state.routes.length - 40) / 2,
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  offlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.md,
    gap: SPACING.xs,
  },
  offlineText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: '600',
  },
  tabBar: {
    paddingTop: SPACING.md,
    paddingBottom: SPACING.xl,
    borderTopLeftRadius: BORDER_RADIUS.xl,
    borderTopRightRadius: BORDER_RADIUS.xl,
    shadowColor: COLORS.shadowDark,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
  },
  tabContent: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    minHeight: 60,
    position: 'relative',
  },
  activeTabBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: BORDER_RADIUS.lg,
  },
  tabIconContainer: {
    position: 'relative',
    marginBottom: SPACING.xs,
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -8,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  tabLabel: {
    fontWeight: '600',
    textAlign: 'center',
  },
  activeIndicator: {
    position: 'absolute',
    bottom: 0,
    width: 40,
    height: 3,
    borderTopLeftRadius: BORDER_RADIUS.sm,
    borderTopRightRadius: BORDER_RADIUS.sm,
  },
});
