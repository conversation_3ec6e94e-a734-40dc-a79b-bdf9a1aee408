import React, { useEffect, useRef } from 'react';
import { Animated, ViewStyle, TextStyle } from 'react-native';

// Animation presets for consistent micro-interactions
export const ANIMATION_PRESETS = {
  fadeIn: {
    duration: 300,
    useNativeDriver: true,
  },
  slideUp: {
    duration: 400,
    useNativeDriver: true,
  },
  scaleIn: {
    duration: 250,
    useNativeDriver: true,
  },
  bounce: {
    duration: 600,
    useNativeDriver: true,
  },
  spring: {
    tension: 100,
    friction: 8,
    useNativeDriver: true,
  },
};

// Fade In Animation Component
interface FadeInViewProps {
  children: React.ReactNode;
  duration?: number;
  delay?: number;
  style?: ViewStyle;
}

export const FadeInView: React.FC<FadeInViewProps> = ({
  children,
  duration = 300,
  delay = 0,
  style,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const timer = setTimeout(() => {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration,
        useNativeDriver: true,
      }).start();
    }, delay);

    return () => clearTimeout(timer);
  }, [fadeAnim, duration, delay]);

  return (
    <Animated.View style={[style, { opacity: fadeAnim }]}>
      {children}
    </Animated.View>
  );
};

// Slide Up Animation Component
interface SlideUpViewProps {
  children: React.ReactNode;
  duration?: number;
  delay?: number;
  distance?: number;
  style?: ViewStyle;
}

export const SlideUpView: React.FC<SlideUpViewProps> = ({
  children,
  duration = 400,
  delay = 0,
  distance = 50,
  style,
}) => {
  const slideAnim = useRef(new Animated.Value(distance)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const timer = setTimeout(() => {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration,
          useNativeDriver: true,
        }),
      ]).start();
    }, delay);

    return () => clearTimeout(timer);
  }, [slideAnim, fadeAnim, duration, delay, distance]);

  return (
    <Animated.View
      style={[
        style,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      {children}
    </Animated.View>
  );
};

// Scale In Animation Component
interface ScaleInViewProps {
  children: React.ReactNode;
  duration?: number;
  delay?: number;
  initialScale?: number;
  style?: ViewStyle;
}

export const ScaleInView: React.FC<ScaleInViewProps> = ({
  children,
  duration = 250,
  delay = 0,
  initialScale = 0.8,
  style,
}) => {
  const scaleAnim = useRef(new Animated.Value(initialScale)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const timer = setTimeout(() => {
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration,
          useNativeDriver: true,
        }),
      ]).start();
    }, delay);

    return () => clearTimeout(timer);
  }, [scaleAnim, fadeAnim, duration, delay, initialScale]);

  return (
    <Animated.View
      style={[
        style,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      {children}
    </Animated.View>
  );
};

// Staggered Animation for Lists
interface StaggeredListProps {
  children: React.ReactNode[];
  staggerDelay?: number;
  animationType?: 'fadeIn' | 'slideUp' | 'scaleIn';
  style?: ViewStyle;
}

export const StaggeredList: React.FC<StaggeredListProps> = ({
  children,
  staggerDelay = 100,
  animationType = 'slideUp',
  style,
}) => {
  const AnimationComponent = {
    fadeIn: FadeInView,
    slideUp: SlideUpView,
    scaleIn: ScaleInView,
  }[animationType];

  return (
    <Animated.View style={style}>
      {children.map((child, index) => (
        <AnimationComponent key={index} delay={index * staggerDelay}>
          {child}
        </AnimationComponent>
      ))}
    </Animated.View>
  );
};

// Pulse Animation for Loading States
interface PulseViewProps {
  children: React.ReactNode;
  duration?: number;
  minOpacity?: number;
  maxOpacity?: number;
  style?: ViewStyle;
}

export const PulseView: React.FC<PulseViewProps> = ({
  children,
  duration = 1000,
  minOpacity = 0.3,
  maxOpacity = 1,
  style,
}) => {
  const pulseAnim = useRef(new Animated.Value(minOpacity)).current;

  useEffect(() => {
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: maxOpacity,
          duration: duration / 2,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: minOpacity,
          duration: duration / 2,
          useNativeDriver: true,
        }),
      ]).start(() => pulse());
    };

    pulse();
  }, [pulseAnim, duration, minOpacity, maxOpacity]);

  return (
    <Animated.View style={[style, { opacity: pulseAnim }]}>
      {children}
    </Animated.View>
  );
};

// Bounce Animation for Interactive Elements
interface BounceViewProps {
  children: React.ReactNode;
  onPress?: () => void;
  bounceScale?: number;
  style?: ViewStyle;
}

export const BounceView: React.FC<BounceViewProps> = ({
  children,
  onPress,
  bounceScale = 0.95,
  style,
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: bounceScale,
      tension: 300,
      friction: 10,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      tension: 300,
      friction: 10,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [{ scale: scaleAnim }],
        },
      ]}
      onTouchStart={handlePressIn}
      onTouchEnd={handlePressOut}
      onTouchCancel={handlePressOut}
    >
      {children}
    </Animated.View>
  );
};

// Shimmer Loading Animation
interface ShimmerViewProps {
  width: number;
  height: number;
  borderRadius?: number;
  style?: ViewStyle;
}

export const ShimmerView: React.FC<ShimmerViewProps> = ({
  width,
  height,
  borderRadius = 8,
  style,
}) => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const shimmer = () => {
      Animated.sequence([
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]).start(() => shimmer());
    };

    shimmer();
  }, [shimmerAnim]);

  const opacity = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          borderRadius,
          backgroundColor: '#E1E9FC',
          opacity,
        },
        style,
      ]}
    />
  );
};
