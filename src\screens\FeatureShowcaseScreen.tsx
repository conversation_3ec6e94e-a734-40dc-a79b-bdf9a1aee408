import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner, ProgressBar, TypingIndicator } from '@/components/ui/LoadingStates';
import { ErrorMessage, NetworkError } from '@/components/ui/ErrorBoundary';
import { useNotifications } from '@/components/ui/NotificationSystem';
import { COLORS, SPACING, BORDER_RADIUS, TYPOGRAPHY } from '@/constants';

interface FeatureCard {
  id: string;
  title: { en: string; ar: string };
  description: { en: string; ar: string };
  icon: string;
  color: string;
  demo: () => void;
}

export const FeatureShowcaseScreen: React.FC = () => {
  const { theme } = useTheme();
  const { language } = useLanguage();
  const { showSuccess, showError, showWarning, showInfo } = useNotifications();
  
  const [loadingStates, setLoadingStates] = useState({
    spinner: false,
    progress: 0,
    typing: false,
    error: false,
    networkError: false,
  });

  const fadeAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  const features: FeatureCard[] = [
    {
      id: 'gemini-2-flash',
      title: { en: 'Gemini 2.0 Flash', ar: 'جيميني 2.0 فلاش' },
      description: { en: 'Updated to latest AI model with better performance', ar: 'محدث لأحدث نموذج ذكي مع أداء أفضل' },
      icon: 'flash',
      color: theme.colors.primary,
      demo: () => {
        showSuccess(
          'Gemini 2.0 Flash',
          'Now using the latest and fastest AI model for better quiz generation and chat responses!'
        );
      },
    },
    {
      id: 'enhanced-error-handling',
      title: { en: 'Smart Error Handling', ar: 'معالجة ذكية للأخطاء' },
      description: { en: 'Automatic retry with rate limiting protection', ar: 'إعادة محاولة تلقائية مع حماية من التحديد' },
      icon: 'shield-checkmark',
      color: theme.colors.success,
      demo: () => {
        setLoadingStates(prev => ({ ...prev, error: true }));
        setTimeout(() => setLoadingStates(prev => ({ ...prev, error: false })), 3000);
      },
    },
    {
      id: 'loading-states',
      title: { en: 'Beautiful Loading States', ar: 'حالات تحميل جميلة' },
      description: { en: 'Smooth animations and progress indicators', ar: 'رسوم متحركة ناعمة ومؤشرات التقدم' },
      icon: 'refresh',
      color: theme.colors.info,
      demo: () => {
        setLoadingStates(prev => ({ ...prev, spinner: true }));
        let progress = 0;
        const interval = setInterval(() => {
          progress += 0.1;
          setLoadingStates(prev => ({ ...prev, progress }));
          if (progress >= 1) {
            clearInterval(interval);
            setTimeout(() => {
              setLoadingStates(prev => ({ ...prev, spinner: false, progress: 0 }));
            }, 500);
          }
        }, 200);
      },
    },
    {
      id: 'typing-indicator',
      title: { en: 'Typing Indicators', ar: 'مؤشرات الكتابة' },
      description: { en: 'Real-time chat typing animations', ar: 'رسوم متحركة للكتابة في الوقت الفعلي' },
      icon: 'chatbubble-ellipses',
      color: theme.colors.secondary,
      demo: () => {
        setLoadingStates(prev => ({ ...prev, typing: true }));
        setTimeout(() => setLoadingStates(prev => ({ ...prev, typing: false })), 3000);
      },
    },
    {
      id: 'notifications',
      title: { en: 'Smart Notifications', ar: 'إشعارات ذكية' },
      description: { en: 'Swipeable notifications with actions', ar: 'إشعارات قابلة للسحب مع إجراءات' },
      icon: 'notifications',
      color: theme.colors.warning,
      demo: () => {
        const notifications = [
          () => showSuccess('Success!', 'Quiz generated successfully with 15 questions'),
          () => showError('Error', 'Failed to connect to API', { label: 'Retry', onPress: () => {} }),
          () => showWarning('Warning', 'You have 3 pending quiz results to sync'),
          () => showInfo('Info', 'New AI features are now available!'),
        ];
        const randomNotification = notifications[Math.floor(Math.random() * notifications.length)];
        randomNotification();
      },
    },
    {
      id: 'offline-support',
      title: { en: 'Offline Support', ar: 'دعم العمل بدون إنترنت' },
      description: { en: 'Continue using the app without internet', ar: 'استمر في استخدام التطبيق بدون إنترنت' },
      icon: 'cloud-offline',
      color: theme.colors.textSecondary,
      demo: () => {
        setLoadingStates(prev => ({ ...prev, networkError: true }));
        setTimeout(() => setLoadingStates(prev => ({ ...prev, networkError: false })), 4000);
      },
    },
    {
      id: 'enhanced-ui',
      title: { en: 'Enhanced UI/UX', ar: 'واجهة محسنة' },
      description: { en: 'Modern design with smooth animations', ar: 'تصميم حديث مع رسوم متحركة ناعمة' },
      icon: 'color-palette',
      color: theme.colors.accent,
      demo: () => {
        showInfo(
          'UI/UX Enhancements',
          'New card layouts, better navigation, improved accessibility, and smooth animations throughout the app!'
        );
      },
    },
    {
      id: 'better-chat',
      title: { en: 'Improved AI Chat', ar: 'محادثة ذكية محسنة' },
      description: { en: 'Context-aware responses with quick actions', ar: 'ردود واعية للسياق مع إجراءات سريعة' },
      icon: 'chatbubbles',
      color: theme.colors.primaryLight,
      demo: () => {
        showSuccess(
          'AI Chat Enhanced',
          'Now with better context understanding, quick action buttons, and improved error handling!'
        );
      },
    },
  ];

  const renderFeatureCard = (feature: FeatureCard, index: number) => {
    return (
      <Animated.View
        key={feature.id}
        style={[
          styles.featureCard,
          {
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
            opacity: fadeAnim,
            transform: [
              {
                translateY: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [50, 0],
                }),
              },
            ],
          },
        ]}
      >
        <TouchableOpacity onPress={feature.demo} style={styles.cardContent}>
          <View style={styles.cardHeader}>
            <View style={[styles.iconContainer, { backgroundColor: `${feature.color}20` }]}>
              <Ionicons name={feature.icon as any} size={24} color={feature.color} />
            </View>
            <View style={styles.cardText}>
              <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
                {feature.title[language] || feature.title.en}
              </Text>
              <Text style={[styles.cardDescription, { color: theme.colors.textSecondary }]}>
                {feature.description[language] || feature.description.en}
              </Text>
            </View>
          </View>
          <View style={styles.demoButton}>
            <Text style={[styles.demoButtonText, { color: feature.color }]}>
              {language === 'ar' ? 'جرب' : 'Try'}
            </Text>
            <Ionicons name="arrow-forward" size={16} color={feature.color} />
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <LinearGradient
        colors={[theme.colors.primary, theme.colors.primaryDark]}
        style={styles.header}
      >
        <Text style={[styles.headerTitle, { color: theme.colors.textOnPrimary }]}>
          {language === 'ar' ? 'الميزات الجديدة' : 'New Features'}
        </Text>
        <Text style={[styles.headerSubtitle, { color: theme.colors.textOnPrimary }]}>
          {language === 'ar' ? 'اكتشف التحسينات الجديدة' : 'Discover the latest improvements'}
        </Text>
      </LinearGradient>

      {/* Demo Components */}
      <View style={styles.demoContainer}>
        {loadingStates.spinner && (
          <View style={styles.demoItem}>
            <LoadingSpinner size="large" />
            <ProgressBar progress={loadingStates.progress} />
          </View>
        )}
        
        {loadingStates.typing && (
          <View style={styles.demoItem}>
            <TypingIndicator visible={true} />
          </View>
        )}
        
        {loadingStates.error && (
          <ErrorMessage
            message="This is a demo error message with retry functionality"
            type="error"
            onRetry={() => setLoadingStates(prev => ({ ...prev, error: false }))}
            visible={true}
          />
        )}
        
        {loadingStates.networkError && (
          <NetworkError
            onRetry={() => setLoadingStates(prev => ({ ...prev, networkError: false }))}
            visible={true}
          />
        )}
      </View>

      {/* Features List */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.featuresContainer}>
          {features.map((feature, index) => renderFeatureCard(feature, index))}
        </View>
        
        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
            {language === 'ar' 
              ? 'جميع الميزات متاحة الآن في التطبيق!' 
              : 'All features are now available in the app!'}
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: SPACING['3xl'],
    paddingTop: SPACING['5xl'],
    borderBottomLeftRadius: BORDER_RADIUS['2xl'],
    borderBottomRightRadius: BORDER_RADIUS['2xl'],
  },
  headerTitle: {
    fontSize: TYPOGRAPHY.fontSize['3xl'],
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  headerSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.base,
    textAlign: 'center',
    opacity: 0.9,
  },
  demoContainer: {
    padding: SPACING.md,
    minHeight: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  demoItem: {
    alignItems: 'center',
    gap: SPACING.md,
  },
  scrollView: {
    flex: 1,
  },
  featuresContainer: {
    padding: SPACING.lg,
    gap: SPACING.md,
  },
  featureCard: {
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    overflow: 'hidden',
    shadowColor: COLORS.shadowLight,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardContent: {
    padding: SPACING.lg,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: SPACING.md,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  cardText: {
    flex: 1,
  },
  cardTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: '600',
    marginBottom: SPACING.xs,
  },
  cardDescription: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    lineHeight: TYPOGRAPHY.lineHeight.normal * TYPOGRAPHY.fontSize.sm,
  },
  demoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: SPACING.xs,
  },
  demoButtonText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
  },
  footer: {
    padding: SPACING.xl,
    alignItems: 'center',
  },
  footerText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
