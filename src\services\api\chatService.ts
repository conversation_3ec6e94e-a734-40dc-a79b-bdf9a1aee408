import { GeminiService } from './geminiService';
import { ApiKeyService } from './apiKeyService';
import { API_CONFIG } from '@/constants';

export interface ChatMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  isTyping?: boolean;
  error?: boolean;
}

export interface ChatContext {
  currentQuiz?: any;
  currentQuestion?: any;
  sourceText?: string;
  userPreferences?: {
    language: 'en' | 'ar';
    responseStyle: 'detailed' | 'concise' | 'educational';
  };
}

export class ChatService {
  private static conversationHistory: ChatMessage[] = [];
  private static context: ChatContext = {};

  static setContext(context: Partial<ChatContext>) {
    this.context = { ...this.context, ...context };
  }

  static getConversationHistory(): ChatMessage[] {
    return this.conversationHistory;
  }

  static addMessage(message: Omit<ChatMessage, 'id' | 'timestamp'>): ChatMessage {
    const newMessage: ChatMessage = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
    };
    
    this.conversationHistory.push(newMessage);
    
    // Keep only last 50 messages to prevent memory issues
    if (this.conversationHistory.length > 50) {
      this.conversationHistory = this.conversationHistory.slice(-50);
    }
    
    return newMessage;
  }

  static async sendMessage(userMessage: string): Promise<ChatMessage> {
    try {
      // Validate API key first
      const hasApiKey = await ApiKeyService.getCurrentApiKey();
      if (!hasApiKey) {
        throw new Error('No API key configured. Please add an API key in Settings to use the chat feature.');
      }

      // Add user message to history
      const userMsg = this.addMessage({
        text: userMessage,
        isUser: true,
      });

      // Create typing indicator
      const typingMsg = this.addMessage({
        text: '',
        isUser: false,
        isTyping: true,
      });

      try {
        // Build context-aware prompt
        const prompt = this.buildContextualPrompt(userMessage);
        
        // Get AI response
        const aiResponse = await GeminiService.getAIExplanation(
          this.context.currentQuestion?.questionText || '',
          this.context.currentQuestion?.correctAnswer || '',
          this.context.sourceText || '',
          prompt
        );

        // Remove typing indicator
        this.conversationHistory = this.conversationHistory.filter(msg => msg.id !== typingMsg.id);

        // Add AI response
        const aiMsg = this.addMessage({
          text: aiResponse,
          isUser: false,
        });

        return aiMsg;
      } catch (error) {
        // Remove typing indicator
        this.conversationHistory = this.conversationHistory.filter(msg => msg.id !== typingMsg.id);
        
        // Add error message
        const errorMsg = this.addMessage({
          text: this.getErrorMessage(error),
          isUser: false,
          error: true,
        });

        throw error;
      }
    } catch (error) {
      console.error('Chat service error:', error);
      throw error;
    }
  }

  private static buildContextualPrompt(userMessage: string): string {
    const { currentQuestion, sourceText, userPreferences } = this.context;
    const language = userPreferences?.language || 'en';
    const responseStyle = userPreferences?.responseStyle || 'educational';

    let contextPrompt = '';

    // Add language preference
    if (language === 'ar') {
      contextPrompt += 'Please respond in Arabic. ';
    } else {
      contextPrompt += 'Please respond in English. ';
    }

    // Add response style
    switch (responseStyle) {
      case 'detailed':
        contextPrompt += 'Provide a detailed and comprehensive explanation. ';
        break;
      case 'concise':
        contextPrompt += 'Keep your response concise and to the point. ';
        break;
      case 'educational':
        contextPrompt += 'Provide an educational explanation that helps with learning. ';
        break;
    }

    // Add current question context if available
    if (currentQuestion) {
      contextPrompt += `\n\nCurrent Question Context:
Question: ${currentQuestion.questionText}
Correct Answer: ${currentQuestion.correctAnswer}
Question Type: ${currentQuestion.questionType}`;
    }

    // Add source text context if available
    if (sourceText) {
      contextPrompt += `\n\nSource Material Context:
${sourceText.substring(0, 1000)}${sourceText.length > 1000 ? '...' : ''}`;
    }

    // Add conversation history for context (last 3 messages)
    const recentHistory = this.conversationHistory
      .filter(msg => !msg.isTyping && !msg.error)
      .slice(-6); // Last 3 exchanges (user + AI)

    if (recentHistory.length > 0) {
      contextPrompt += '\n\nRecent Conversation:';
      recentHistory.forEach(msg => {
        contextPrompt += `\n${msg.isUser ? 'User' : 'AI'}: ${msg.text}`;
      });
    }

    contextPrompt += `\n\nUser's Current Question: ${userMessage}`;

    return contextPrompt;
  }

  private static getErrorMessage(error: any): string {
    const errorMessage = error?.message || 'Unknown error occurred';
    
    if (errorMessage.includes('rate limit') || errorMessage.includes('429')) {
      return 'I\'m currently experiencing high demand. Please wait a moment and try again.';
    } else if (errorMessage.includes('API key')) {
      return 'There seems to be an issue with the API configuration. Please check your settings.';
    } else if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
      return 'I\'m having trouble connecting right now. Please check your internet connection and try again.';
    } else {
      return 'I encountered an error while processing your request. Please try again.';
    }
  }

  static clearHistory() {
    this.conversationHistory = [];
  }

  static removeMessage(messageId: string) {
    this.conversationHistory = this.conversationHistory.filter(msg => msg.id !== messageId);
  }

  static updateMessage(messageId: string, updates: Partial<ChatMessage>) {
    const messageIndex = this.conversationHistory.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1) {
      this.conversationHistory[messageIndex] = {
        ...this.conversationHistory[messageIndex],
        ...updates,
      };
    }
  }

  // Quick action methods
  static async explainAnswer(question: any): Promise<ChatMessage> {
    const prompt = `Can you explain why "${question.correctAnswer}" is the correct answer for this question?`;
    return this.sendMessage(prompt);
  }

  static async askForHint(question: any): Promise<ChatMessage> {
    const prompt = `Can you give me a hint for this question without revealing the answer?`;
    return this.sendMessage(prompt);
  }

  static async requestSimilarQuestions(question: any): Promise<ChatMessage> {
    const prompt = `Can you suggest similar questions or topics I should study related to this question?`;
    return this.sendMessage(prompt);
  }

  static async explainConcept(concept: string): Promise<ChatMessage> {
    const prompt = `Can you explain the concept of "${concept}" in detail?`;
    return this.sendMessage(prompt);
  }
}
