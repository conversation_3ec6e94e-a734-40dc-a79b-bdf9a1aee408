import axios from 'axios';
import { API_CONFIG } from '@/constants';
import { Question, QuizGenerationParams } from '@/types';
import { ApiKeyService } from './apiKeyService';

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
  }>;
}

export class GeminiService {
  private static apiUrl = API_CONFIG.GEMINI_API_URL;
  private static visionApiUrl = API_CONFIG.GEMINI_VISION_API_URL;

  private static async getApiKey(): Promise<string> {
    const apiKey = await ApiKeyService.getCurrentApiKey();
    if (!apiKey) {
      throw new Error('No API key configured. Please add an API key in Settings.');
    }
    return apiKey;
  }

  private static async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private static isRateLimitError(error: any): boolean {
    return error?.response?.status === 429 ||
           error?.message?.includes('429') ||
           error?.message?.includes('rate limit') ||
           error?.message?.includes('quota exceeded');
  }

  private static isRetryableError(error: any): boolean {
    const status = error?.response?.status;
    return status === 429 || status === 500 || status === 502 || status === 503 || status === 504;
  }

  static async extractTextFromFile(fileUri: string, fileType: 'pdf' | 'image'): Promise<string> {
    try {
      // For demo purposes, we'll return comprehensive sample text based on file type
      // In production, you would:
      // 1. Convert the file to base64
      // 2. Send it to Gemini Vision API with the file data

      const sampleTexts = {
        pdf: `
# Chapter 1: Introduction to Computer Science

Computer Science is the study of computational systems and the design of computer systems and their applications. It encompasses both the theoretical foundations of computing and practical techniques for their implementation and application in computer systems.

## Key Concepts:
- **Algorithms**: Step-by-step procedures for solving problems
- **Data Structures**: Ways of organizing and storing data
- **Programming Languages**: Tools for implementing solutions
- **Software Engineering**: Systematic approach to software development

## Historical Development:
The field of computer science emerged in the mid-20th century, building on mathematical foundations laid by pioneers like Alan Turing and John von Neumann.

## Applications:
- Artificial Intelligence and Machine Learning
- Web Development and Mobile Applications
- Database Management Systems
- Computer Networks and Security
- Human-Computer Interaction

## Programming Fundamentals:
Programming is the process of creating instructions for computers to execute. Key principles include:
1. Problem decomposition
2. Algorithm design
3. Code implementation
4. Testing and debugging
5. Documentation and maintenance

This comprehensive introduction provides the foundation for understanding more advanced topics in computer science.
        `,
        image: `
# Mathematics Formula Sheet

## Algebra:
- Quadratic Formula: x = (-b ± √(b² - 4ac)) / 2a
- Slope-Intercept Form: y = mx + b
- Point-Slope Form: y - y₁ = m(x - x₁)

## Geometry:
- Area of Circle: A = πr²
- Circumference: C = 2πr
- Pythagorean Theorem: a² + b² = c²
- Area of Triangle: A = ½bh

## Trigonometry:
- sin²θ + cos²θ = 1
- tan θ = sin θ / cos θ
- Law of Sines: a/sin A = b/sin B = c/sin C
- Law of Cosines: c² = a² + b² - 2ab cos C

## Calculus:
- Derivative of xⁿ: nxⁿ⁻¹
- Integral of xⁿ: xⁿ⁺¹/(n+1) + C
- Chain Rule: (f(g(x)))' = f'(g(x)) · g'(x)
- Product Rule: (fg)' = f'g + fg'

These formulas are essential for solving mathematical problems across various disciplines.
        `
      };

      const extractedText = sampleTexts[fileType];

      // Simulate processing delay
      await this.delay(1500);

      return extractedText.trim();
    } catch (error) {
      console.error('Error extracting text:', error);
      throw new Error(`Failed to extract text from ${fileType} file. Please ensure the file is valid and try again.`);
    }
  }

  static async generateQuiz(params: QuizGenerationParams, extractedText: string): Promise<Question[]> {
    const prompt = this.buildQuizGenerationPrompt(params, extractedText);

    try {
      const response = await this.sendRequest(prompt);
      const questions = this.parseQuizResponse(response);
      return questions;
    } catch (error) {
      console.error('Error generating quiz:', error);
      throw new Error('Failed to generate quiz');
    }
  }

  static async getAIExplanation(
    question: string,
    correctAnswer: string,
    sourceText: string,
    userQuery: string
  ): Promise<string> {
    const prompt = `
      Context: The user is asking about this quiz question: "${question}"
      The correct answer is: "${correctAnswer}"
      This is based on the following source text: "${sourceText}"

      User's question: "${userQuery}"

      Please provide a helpful explanation that helps the user understand the concept better.
      Keep your response conversational and educational.
    `;

    try {
      const response = await this.sendRequest(prompt);
      return response;
    } catch (error) {
      console.error('Error getting AI explanation:', error);
      throw new Error('Failed to get AI explanation');
    }
  }

  private static buildQuizGenerationPrompt(params: QuizGenerationParams, extractedText: string): string {
    const { subject, questionType, numberOfQuestions, focusAreas, materialScope } = params;

    let questionTypeInstruction = '';
    switch (questionType) {
      case 'multiple_choice':
        questionTypeInstruction = 'Each question should have 4 options (A, B, C, D).';
        break;
      case 'true_false':
        questionTypeInstruction = 'Each question should be answerable with True or False.';
        break;
      case 'explain_justify':
        questionTypeInstruction = 'Each question should require a written explanation or justification.';
        break;
      case 'mix':
        questionTypeInstruction = 'Include a mix of multiple choice, true/false, and explanation questions.';
        break;
    }

    return `
You are an expert quiz generation AI. Your task is to create a quiz based on the provided text. Follow these instructions precisely:

Context Text:
---
${extractedText}
---

Quiz Parameters:
- Subject: ${subject}
- Number of Questions: ${numberOfQuestions}
- Question Types: ${questionType}
${focusAreas ? `- User Notes/Focus Areas: ${focusAreas}` : ''}
${materialScope ? `- Material Scope: ${materialScope}` : ''}

Instructions:
1. Generate exactly ${numberOfQuestions} questions based *only* on the provided Context Text.
2. ${questionTypeInstruction}
3. For each question, you MUST find the direct quote or sentence in the Context Text that serves as the source for the answer.
4. Your entire response MUST be a valid JSON array.
5. Each object in the array represents a single question and must have the following exact structure:
   {
     "questionText": "The full text of the question.",
     "questionType": "${questionType === 'mix' ? 'one of: multiple_choice, true_false, or explain_justify' : questionType}",
     "options": ["Option A", "Option B", "Option C", "Option D"],
     "correctAnswer": "The exact text of the correct option",
     "sourceQuote": "The exact sentence or short paragraph from the context text that justifies the answer.",
     "sourceLocation": {
       "page": 1
     }
   }
6. For "true_false" questions, the "options" array should be ["True", "False"].
7. For "explain_justify" questions, the "options" array should be empty [], and the "correctAnswer" should contain a model answer.
8. Ensure questions test understanding, not just memorization.
9. Questions should be clear and unambiguous.
10. The difficulty should be appropriate for the subject level.

IMPORTANT: Return ONLY the JSON array, no additional text or formatting.
`;
  }

  private static async sendRequest(prompt: string, useVision: boolean = false): Promise<string> {
    const apiKey = await this.getApiKey();
    const apiUrl = useVision ? this.visionApiUrl : this.apiUrl;

    let lastError: any;

    for (let attempt = 1; attempt <= API_CONFIG.MAX_RETRIES; attempt++) {
      try {
        const response = await axios.post<GeminiResponse>(
          `${apiUrl}?key=${apiKey}`,
          {
            contents: [
              {
                parts: [
                  {
                    text: prompt,
                  },
                ],
              },
            ],
            generationConfig: {
              temperature: 0.7,
              topK: 40,
              topP: 0.95,
              maxOutputTokens: 8192,
            },
            safetySettings: [
              {
                category: "HARM_CATEGORY_HARASSMENT",
                threshold: "BLOCK_MEDIUM_AND_ABOVE"
              },
              {
                category: "HARM_CATEGORY_HATE_SPEECH",
                threshold: "BLOCK_MEDIUM_AND_ABOVE"
              },
              {
                category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                threshold: "BLOCK_MEDIUM_AND_ABOVE"
              },
              {
                category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                threshold: "BLOCK_MEDIUM_AND_ABOVE"
              }
            ]
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
            timeout: API_CONFIG.REQUEST_TIMEOUT,
          }
        );

        if (response.data.candidates && response.data.candidates.length > 0) {
          const candidate = response.data.candidates[0];
          if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
            return candidate.content.parts[0].text;
          } else {
            throw new Error('Invalid response structure from Gemini API');
          }
        } else {
          throw new Error('No response candidates from Gemini API');
        }
      } catch (error: any) {
        lastError = error;
        console.error(`Gemini API error (attempt ${attempt}/${API_CONFIG.MAX_RETRIES}):`, error);

        // Handle specific error types
        if (this.isRateLimitError(error)) {
          if (attempt < API_CONFIG.MAX_RETRIES) {
            const delayMs = API_CONFIG.RETRY_DELAY * Math.pow(2, attempt - 1); // Exponential backoff
            console.log(`Rate limited. Retrying in ${delayMs}ms...`);
            await this.delay(delayMs);
            continue;
          } else {
            throw new Error('API rate limit exceeded. Please try again later or check your API quota.');
          }
        }

        // For other retryable errors
        if (this.isRetryableError(error) && attempt < API_CONFIG.MAX_RETRIES) {
          const delayMs = API_CONFIG.RETRY_DELAY * attempt;
          console.log(`Retryable error. Retrying in ${delayMs}ms...`);
          await this.delay(delayMs);
          continue;
        }

        // For non-retryable errors, throw immediately
        if (!this.isRetryableError(error)) {
          break;
        }
      }
    }

    // If we get here, all retries failed
    if (this.isRateLimitError(lastError)) {
      throw new Error('API rate limit exceeded. Please try again later or check your API quota.');
    } else if (lastError?.response?.status === 401) {
      throw new Error('Invalid API key. Please check your API key in Settings.');
    } else if (lastError?.response?.status === 403) {
      throw new Error('API access forbidden. Please check your API key permissions.');
    } else {
      throw new Error(`Failed to communicate with Gemini API: ${lastError?.message || 'Unknown error'}`);
    }
  }

  private static parseQuizResponse(response: string): Question[] {
    try {
      // Clean the response to extract JSON
      const jsonMatch = response.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in response');
      }

      const questionsData = JSON.parse(jsonMatch[0]);

      return questionsData.map((q: any, index: number) => ({
        id: `q_${Date.now()}_${index}`,
        questionText: q.questionText,
        questionType: q.questionType,
        options: q.options || [],
        correctAnswer: q.correctAnswer,
        sourceQuote: q.sourceQuote,
        sourceLocation: q.sourceLocation || { page: 1 },
        explanation: q.explanation,
      }));
    } catch (error) {
      console.error('Error parsing quiz response:', error);
      throw new Error('Failed to parse quiz response');
    }
  }

  // Mock method for demo purposes - generates sample questions
  static generateSampleQuiz(params: QuizGenerationParams): Question[] {
    const questionTemplates = [
      {
        questionText: 'What is the main purpose of the Test Me application?',
        questionType: 'multiple_choice' as const,
        options: [
          'To create social media content',
          'To generate quizzes from study materials',
          'To play games',
          'To manage files'
        ],
        correctAnswer: 'To generate quizzes from study materials',
        explanation: 'The Test Me application is designed specifically for educational purposes, allowing students to upload their study materials and automatically generate quizzes to test their knowledge.'
      },
      {
        questionText: 'The app supports both Arabic and English languages.',
        questionType: 'true_false' as const,
        options: ['True', 'False'],
        correctAnswer: 'True',
        explanation: 'Yes, the Test Me app is designed to support both Arabic and English languages to cater to a diverse user base.'
      },
      {
        questionText: `Which file types are supported for upload in ${params.subject}?`,
        questionType: 'multiple_choice' as const,
        options: ['PDF and Images', 'Only PDF', 'Only Images', 'Text files only'],
        correctAnswer: 'PDF and Images',
        explanation: 'The application supports both PDF documents and image files for quiz generation.'
      },
      {
        questionText: `AI-powered quiz generation is available in this ${params.subject} app.`,
        questionType: 'true_false' as const,
        options: ['True', 'False'],
        correctAnswer: 'True',
        explanation: 'The app uses Google Gemini AI to automatically generate quiz questions from uploaded materials.'
      },
      {
        questionText: `What is the primary benefit of using AI for ${params.subject} quiz generation?`,
        questionType: 'multiple_choice' as const,
        options: ['Faster processing', 'Personalized questions', 'Better accuracy', 'All of the above'],
        correctAnswer: 'All of the above',
        explanation: 'AI-powered quiz generation provides faster processing, creates personalized questions based on the content, and maintains high accuracy.'
      }
    ];

    const questions: Question[] = [];
    const questionTypes = params.questionType === 'mix'
      ? ['multiple_choice', 'true_false', 'explain_justify']
      : [params.questionType];

    for (let i = 0; i < params.numberOfQuestions; i++) {
      const templateIndex = i % questionTemplates.length;
      const template = questionTemplates[templateIndex];

      // For mix type, vary the question types
      let questionType = template.questionType;
      if (params.questionType === 'mix') {
        questionType = questionTypes[i % questionTypes.length] as any;
      }

      questions.push({
        id: `sample_${i + 1}`,
        questionText: template.questionText,
        questionType: questionType,
        options: template.options,
        correctAnswer: template.correctAnswer,
        sourceQuote: `Sample content from ${params.subject} materials.`,
        sourceLocation: { page: Math.floor(i / 2) + 1 },
        explanation: template.explanation
      });
    }

    return questions;
  }
}
