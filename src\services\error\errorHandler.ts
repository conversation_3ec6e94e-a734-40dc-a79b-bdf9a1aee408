import { Alert } from 'react-native';
import { AsyncStorageService } from '../storage/asyncStorage';

// Error Types
export enum ErrorType {
  NETWORK = 'NETWORK',
  API = 'API',
  STORAGE = 'STORAGE',
  FILE_PROCESSING = 'FILE_PROCESSING',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  PERMISSION = 'PERMISSION',
  UNKNOWN = 'UNKNOWN',
}

// Error Severity Levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

// Error Interface
export interface AppError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  details?: any;
  timestamp: string;
  userId?: string;
  context?: {
    screen?: string;
    action?: string;
    metadata?: Record<string, any>;
  };
  stack?: string;
}

// Error Handler Class
export class ErrorHandler {
  private static errorLog: AppError[] = [];
  private static maxLogSize = 100;

  // Log an error
  static async logError(error: Partial<AppError>): Promise<void> {
    const appError: AppError = {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: error.type || ErrorType.UNKNOWN,
      severity: error.severity || ErrorSeverity.MEDIUM,
      message: error.message || 'An unknown error occurred',
      details: error.details,
      timestamp: new Date().toISOString(),
      userId: error.userId,
      context: error.context,
      stack: error.stack,
    };

    // Add to in-memory log
    this.errorLog.unshift(appError);
    
    // Keep log size manageable
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }

    // Persist to storage
    try {
      await AsyncStorageService.setItem('error_log', JSON.stringify(this.errorLog));
    } catch (storageError) {
      console.error('Failed to persist error log:', storageError);
    }

    // Log to console in development
    if (__DEV__) {
      console.error('App Error:', appError);
    }

    // Handle critical errors immediately
    if (appError.severity === ErrorSeverity.CRITICAL) {
      this.handleCriticalError(appError);
    }
  }

  // Handle different types of errors
  static async handleError(
    error: Error | any,
    context?: AppError['context'],
    showUserAlert = true
  ): Promise<void> {
    const errorType = this.determineErrorType(error);
    const severity = this.determineSeverity(error, errorType);
    
    const appError: Partial<AppError> = {
      type: errorType,
      severity,
      message: this.getErrorMessage(error),
      details: error,
      context,
      stack: error?.stack,
    };

    await this.logError(appError);

    if (showUserAlert) {
      this.showUserFriendlyError(appError);
    }
  }

  // Determine error type from error object
  private static determineErrorType(error: any): ErrorType {
    if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('network')) {
      return ErrorType.NETWORK;
    }
    if (error?.response?.status || error?.message?.includes('API')) {
      return ErrorType.API;
    }
    if (error?.message?.includes('storage') || error?.message?.includes('AsyncStorage')) {
      return ErrorType.STORAGE;
    }
    if (error?.message?.includes('file') || error?.message?.includes('upload')) {
      return ErrorType.FILE_PROCESSING;
    }
    if (error?.message?.includes('validation') || error?.message?.includes('invalid')) {
      return ErrorType.VALIDATION;
    }
    if (error?.message?.includes('permission') || error?.message?.includes('unauthorized')) {
      return ErrorType.PERMISSION;
    }
    if (error?.message?.includes('auth') || error?.message?.includes('token')) {
      return ErrorType.AUTHENTICATION;
    }
    
    return ErrorType.UNKNOWN;
  }

  // Determine error severity
  private static determineSeverity(error: any, type: ErrorType): ErrorSeverity {
    // Critical errors that break core functionality
    if (type === ErrorType.STORAGE && error?.message?.includes('clear')) {
      return ErrorSeverity.CRITICAL;
    }
    
    // High severity errors
    if (type === ErrorType.API && error?.response?.status >= 500) {
      return ErrorSeverity.HIGH;
    }
    if (type === ErrorType.AUTHENTICATION) {
      return ErrorSeverity.HIGH;
    }
    
    // Medium severity errors
    if (type === ErrorType.NETWORK || type === ErrorType.FILE_PROCESSING) {
      return ErrorSeverity.MEDIUM;
    }
    
    // Low severity errors
    if (type === ErrorType.VALIDATION) {
      return ErrorSeverity.LOW;
    }
    
    return ErrorSeverity.MEDIUM;
  }

  // Get user-friendly error message
  private static getErrorMessage(error: any): string {
    if (typeof error === 'string') return error;
    if (error?.message) return error.message;
    if (error?.response?.data?.message) return error.response.data.message;
    return 'An unexpected error occurred';
  }

  // Show user-friendly error alert
  private static showUserFriendlyError(error: Partial<AppError>): void {
    const userMessage = this.getUserFriendlyMessage(error);
    const title = this.getErrorTitle(error.type!);

    Alert.alert(
      title,
      userMessage,
      [
        { text: 'OK', style: 'default' },
        ...(error.severity === ErrorSeverity.HIGH || error.severity === ErrorSeverity.CRITICAL
          ? [{ text: 'Report Issue', onPress: () => this.reportIssue(error) }]
          : []
        ),
      ]
    );
  }

  // Get user-friendly error message
  private static getUserFriendlyMessage(error: Partial<AppError>): string {
    switch (error.type) {
      case ErrorType.NETWORK:
        return 'Please check your internet connection and try again.';
      case ErrorType.API:
        return 'We\'re experiencing server issues. Please try again later.';
      case ErrorType.STORAGE:
        return 'There was an issue saving your data. Please try again.';
      case ErrorType.FILE_PROCESSING:
        return 'There was an issue processing your file. Please check the file format and try again.';
      case ErrorType.VALIDATION:
        return 'Please check your input and try again.';
      case ErrorType.AUTHENTICATION:
        return 'Please check your API key in Settings and try again.';
      case ErrorType.PERMISSION:
        return 'Permission denied. Please check your settings.';
      default:
        return 'Something went wrong. Please try again.';
    }
  }

  // Get error title
  private static getErrorTitle(type: ErrorType): string {
    switch (type) {
      case ErrorType.NETWORK:
        return 'Connection Error';
      case ErrorType.API:
        return 'Server Error';
      case ErrorType.STORAGE:
        return 'Storage Error';
      case ErrorType.FILE_PROCESSING:
        return 'File Error';
      case ErrorType.VALIDATION:
        return 'Input Error';
      case ErrorType.AUTHENTICATION:
        return 'Authentication Error';
      case ErrorType.PERMISSION:
        return 'Permission Error';
      default:
        return 'Error';
    }
  }

  // Handle critical errors
  private static handleCriticalError(error: AppError): void {
    // Log critical error with more detail
    console.error('CRITICAL ERROR:', error);
    
    // Could implement crash reporting here
    // Could implement automatic recovery mechanisms
    
    Alert.alert(
      'Critical Error',
      'A critical error has occurred. The app may need to restart.',
      [
        { text: 'Report Issue', onPress: () => this.reportIssue(error) },
        { text: 'Continue', style: 'cancel' },
      ]
    );
  }

  // Report issue (placeholder for future implementation)
  private static reportIssue(error: Partial<AppError>): void {
    // Could implement issue reporting to external service
    console.log('Reporting issue:', error);
  }

  // Get error log
  static async getErrorLog(): Promise<AppError[]> {
    try {
      const stored = await AsyncStorageService.getItem('error_log');
      if (stored) {
        this.errorLog = JSON.parse(stored);
      }
      return this.errorLog;
    } catch (error) {
      console.error('Failed to load error log:', error);
      return [];
    }
  }

  // Clear error log
  static async clearErrorLog(): Promise<void> {
    try {
      this.errorLog = [];
      await AsyncStorageService.removeItem('error_log');
    } catch (error) {
      console.error('Failed to clear error log:', error);
    }
  }

  // Get error statistics
  static getErrorStats(): {
    total: number;
    byType: Record<ErrorType, number>;
    bySeverity: Record<ErrorSeverity, number>;
    recent: number;
  } {
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const byType = Object.values(ErrorType).reduce((acc, type) => {
      acc[type] = this.errorLog.filter(error => error.type === type).length;
      return acc;
    }, {} as Record<ErrorType, number>);

    const bySeverity = Object.values(ErrorSeverity).reduce((acc, severity) => {
      acc[severity] = this.errorLog.filter(error => error.severity === severity).length;
      return acc;
    }, {} as Record<ErrorSeverity, number>);

    const recent = this.errorLog.filter(
      error => new Date(error.timestamp) > oneDayAgo
    ).length;

    return {
      total: this.errorLog.length,
      byType,
      bySeverity,
      recent,
    };
  }
}
