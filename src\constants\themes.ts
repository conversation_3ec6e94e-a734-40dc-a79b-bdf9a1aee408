import { SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from './index';

// Theme Types
export type ThemeMode = 'light' | 'dark';

export interface ThemeColors {
  // Primary Colors
  primary: string;
  primaryDark: string;
  primaryLight: string;
  secondary: string;
  secondaryDark: string;
  accent: string;
  
  // Background Colors
  background: string;
  surface: string;
  surfaceSecondary: string;
  cardBackground: string;
  
  // Text Colors
  text: string;
  textSecondary: string;
  textOnPrimary: string;
  textOnSurface: string;
  
  // Border Colors
  border: string;
  borderLight: string;
  borderDark: string;
  
  // Status Colors
  success: string;
  warning: string;
  error: string;
  info: string;
  
  // Overlay Colors
  overlay: string;
  modalBackground: string;
  
  // Shadow Colors
  shadowColor: string;
  
  // Gradient Colors
  gradientStart: string;
  gradientEnd: string;
}

export interface Theme {
  mode: ThemeMode;
  colors: ThemeColors;
  spacing: typeof SPACING;
  typography: typeof TYPOGRAPHY;
  borderRadius: typeof BORDER_RADIUS;
  shadows: typeof SHADOWS;
}

// Light Theme Colors
const lightColors: ThemeColors = {
  // Primary Colors - Sophisticated Purple System
  primary: '#8B5CF6',
  primaryDark: '#7C3AED',
  primaryLight: '#A78BFA',
  secondary: '#06B6D4',
  secondaryDark: '#0891B2',
  accent: '#F59E0B',
  
  // Background Colors - Purple-tinted alternatives to white
  background: '#FAFBFF',
  surface: '#F8FAFF',
  surfaceSecondary: '#F1F5FF',
  cardBackground: '#FFFFFF',
  
  // Text Colors
  text: '#1F2937',
  textSecondary: '#6B7280',
  textOnPrimary: '#FFFFFF',
  textOnSurface: '#374151',
  
  // Border Colors
  border: '#E5E7EB',
  borderLight: '#F3F4F6',
  borderDark: '#D1D5DB',
  
  // Status Colors
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  
  // Overlay Colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  modalBackground: 'rgba(0, 0, 0, 0.8)',
  
  // Shadow Colors
  shadowColor: '#8B5CF6',
  
  // Gradient Colors
  gradientStart: '#8B5CF6',
  gradientEnd: '#7C3AED',
};

// Dark Theme Colors
const darkColors: ThemeColors = {
  // Primary Colors - Enhanced for dark mode
  primary: '#A78BFA',
  primaryDark: '#8B5CF6',
  primaryLight: '#C4B5FD',
  secondary: '#22D3EE',
  secondaryDark: '#06B6D4',
  accent: '#FBBF24',
  
  // Background Colors - Dark purple-tinted system
  background: '#0F0F23',
  surface: '#1A1A2E',
  surfaceSecondary: '#16213E',
  cardBackground: '#252545',
  
  // Text Colors - High contrast for accessibility
  text: '#F9FAFB',
  textSecondary: '#D1D5DB',
  textOnPrimary: '#1F2937',
  textOnSurface: '#E5E7EB',
  
  // Border Colors
  border: '#374151',
  borderLight: '#4B5563',
  borderDark: '#1F2937',
  
  // Status Colors - Adjusted for dark mode
  success: '#34D399',
  warning: '#FBBF24',
  error: '#F87171',
  info: '#60A5FA',
  
  // Overlay Colors
  overlay: 'rgba(0, 0, 0, 0.7)',
  modalBackground: 'rgba(0, 0, 0, 0.9)',
  
  // Shadow Colors
  shadowColor: '#000000',
  
  // Gradient Colors
  gradientStart: '#A78BFA',
  gradientEnd: '#8B5CF6',
};

// Theme Objects
export const lightTheme: Theme = {
  mode: 'light',
  colors: lightColors,
  spacing: SPACING,
  typography: TYPOGRAPHY,
  borderRadius: BORDER_RADIUS,
  shadows: {
    ...SHADOWS,
    // Adjust shadows for light theme
    sm: {
      shadowColor: lightColors.shadowColor,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowColor: lightColors.shadowColor,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 4,
      elevation: 4,
    },
    lg: {
      shadowColor: lightColors.shadowColor,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 8,
    },
    xl: {
      shadowColor: lightColors.shadowColor,
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.25,
      shadowRadius: 16,
      elevation: 16,
    },
  },
};

export const darkTheme: Theme = {
  mode: 'dark',
  colors: darkColors,
  spacing: SPACING,
  typography: TYPOGRAPHY,
  borderRadius: BORDER_RADIUS,
  shadows: {
    ...SHADOWS,
    // Adjust shadows for dark theme
    sm: {
      shadowColor: darkColors.shadowColor,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.3,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowColor: darkColors.shadowColor,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.4,
      shadowRadius: 4,
      elevation: 4,
    },
    lg: {
      shadowColor: darkColors.shadowColor,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.5,
      shadowRadius: 8,
      elevation: 8,
    },
    xl: {
      shadowColor: darkColors.shadowColor,
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.6,
      shadowRadius: 16,
      elevation: 16,
    },
  },
};

// Theme Utilities
export const getTheme = (mode: ThemeMode): Theme => {
  return mode === 'dark' ? darkTheme : lightTheme;
};

export const getContrastColor = (backgroundColor: string, theme: Theme): string => {
  // Simple contrast calculation - in production, you might want a more sophisticated algorithm
  const isDark = theme.mode === 'dark';
  return isDark ? theme.colors.text : theme.colors.textOnSurface;
};

// Gradient Utilities for both themes
export const getGradientColors = (theme: Theme, variant: 'primary' | 'secondary' | 'accent' = 'primary') => {
  switch (variant) {
    case 'primary':
      return [theme.colors.gradientStart, theme.colors.gradientEnd];
    case 'secondary':
      return [theme.colors.secondary, theme.colors.secondaryDark];
    case 'accent':
      return [theme.colors.accent, theme.colors.warning];
    default:
      return [theme.colors.gradientStart, theme.colors.gradientEnd];
  }
};

// Status Bar Style
export const getStatusBarStyle = (theme: Theme): 'light-content' | 'dark-content' => {
  return theme.mode === 'dark' ? 'light-content' : 'dark-content';
};

// Theme Animation Duration
export const THEME_TRANSITION_DURATION = 300;
