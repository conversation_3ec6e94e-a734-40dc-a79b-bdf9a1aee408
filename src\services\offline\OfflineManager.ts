import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { STORAGE_KEYS } from '@/constants';

export interface OfflineAction {
  id: string;
  type: 'SEND_MESSAGE' | 'GENERATE_QUIZ' | 'SAVE_QUIZ_RESULT' | 'SYNC_SETTINGS';
  payload: any;
  timestamp: Date;
  retryCount: number;
  maxRetries: number;
}

export interface OfflineData {
  quizzes: any[];
  chatHistory: any[];
  userSettings: any;
  quizResults: any[];
  lastSyncTimestamp: Date | null;
}

class OfflineManagerClass {
  private isOnline: boolean = true;
  private pendingActions: OfflineAction[] = [];
  private syncInProgress: boolean = false;
  private listeners: Array<(isOnline: boolean) => void> = [];

  constructor() {
    this.initializeNetworkListener();
    this.loadPendingActions();
  }

  private async initializeNetworkListener() {
    // Listen for network state changes
    NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      if (!wasOnline && this.isOnline) {
        // Just came back online, sync pending actions
        this.syncPendingActions();
      }
      
      // Notify listeners
      this.listeners.forEach(listener => listener(this.isOnline));
    });

    // Get initial network state
    const state = await NetInfo.fetch();
    this.isOnline = state.isConnected ?? false;
  }

  public addNetworkListener(listener: (isOnline: boolean) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  public getNetworkStatus(): boolean {
    return this.isOnline;
  }

  public async addPendingAction(action: Omit<OfflineAction, 'id' | 'timestamp' | 'retryCount'>): Promise<void> {
    const pendingAction: OfflineAction = {
      ...action,
      id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      retryCount: 0,
    };

    this.pendingActions.push(pendingAction);
    await this.savePendingActions();

    // If online, try to sync immediately
    if (this.isOnline) {
      this.syncPendingActions();
    }
  }

  private async loadPendingActions(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.OFFLINE_ACTIONS || '@offline_actions');
      if (stored) {
        this.pendingActions = JSON.parse(stored).map((action: any) => ({
          ...action,
          timestamp: new Date(action.timestamp),
        }));
      }
    } catch (error) {
      console.error('Error loading pending actions:', error);
    }
  }

  private async savePendingActions(): Promise<void> {
    try {
      await AsyncStorage.setItem(
        STORAGE_KEYS.OFFLINE_ACTIONS || '@offline_actions',
        JSON.stringify(this.pendingActions)
      );
    } catch (error) {
      console.error('Error saving pending actions:', error);
    }
  }

  public async syncPendingActions(): Promise<void> {
    if (!this.isOnline || this.syncInProgress || this.pendingActions.length === 0) {
      return;
    }

    this.syncInProgress = true;

    try {
      const actionsToProcess = [...this.pendingActions];
      
      for (const action of actionsToProcess) {
        try {
          await this.processAction(action);
          // Remove successful action
          this.pendingActions = this.pendingActions.filter(a => a.id !== action.id);
        } catch (error) {
          console.error(`Error processing action ${action.id}:`, error);
          
          // Increment retry count
          const actionIndex = this.pendingActions.findIndex(a => a.id === action.id);
          if (actionIndex !== -1) {
            this.pendingActions[actionIndex].retryCount++;
            
            // Remove if max retries exceeded
            if (this.pendingActions[actionIndex].retryCount >= action.maxRetries) {
              this.pendingActions.splice(actionIndex, 1);
              console.warn(`Action ${action.id} exceeded max retries and was removed`);
            }
          }
        }
      }

      await this.savePendingActions();
    } finally {
      this.syncInProgress = false;
    }
  }

  private async processAction(action: OfflineAction): Promise<void> {
    switch (action.type) {
      case 'SEND_MESSAGE':
        // Process chat message
        // This would integrate with your ChatService
        break;
      
      case 'GENERATE_QUIZ':
        // Process quiz generation
        // This would integrate with your GeminiService
        break;
      
      case 'SAVE_QUIZ_RESULT':
        // Save quiz result to server/cloud
        break;
      
      case 'SYNC_SETTINGS':
        // Sync user settings
        break;
      
      default:
        throw new Error(`Unknown action type: ${action.type}`);
    }
  }

  // Offline data management
  public async saveOfflineData(key: keyof OfflineData, data: any): Promise<void> {
    try {
      const storageKey = `@offline_${key}`;
      await AsyncStorage.setItem(storageKey, JSON.stringify(data));
    } catch (error) {
      console.error(`Error saving offline data for ${key}:`, error);
    }
  }

  public async getOfflineData<T>(key: keyof OfflineData): Promise<T | null> {
    try {
      const storageKey = `@offline_${key}`;
      const stored = await AsyncStorage.getItem(storageKey);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error(`Error getting offline data for ${key}:`, error);
      return null;
    }
  }

  public async clearOfflineData(key?: keyof OfflineData): Promise<void> {
    try {
      if (key) {
        const storageKey = `@offline_${key}`;
        await AsyncStorage.removeItem(storageKey);
      } else {
        // Clear all offline data
        const keys = ['quizzes', 'chatHistory', 'userSettings', 'quizResults', 'lastSyncTimestamp'];
        await Promise.all(
          keys.map(k => AsyncStorage.removeItem(`@offline_${k}`))
        );
      }
    } catch (error) {
      console.error('Error clearing offline data:', error);
    }
  }

  // Quiz-specific offline functionality
  public async saveQuizForOffline(quiz: any): Promise<void> {
    const offlineQuizzes = await this.getOfflineData<any[]>('quizzes') || [];
    const existingIndex = offlineQuizzes.findIndex(q => q.id === quiz.id);
    
    if (existingIndex !== -1) {
      offlineQuizzes[existingIndex] = quiz;
    } else {
      offlineQuizzes.push(quiz);
    }
    
    await this.saveOfflineData('quizzes', offlineQuizzes);
  }

  public async getOfflineQuizzes(): Promise<any[]> {
    return await this.getOfflineData<any[]>('quizzes') || [];
  }

  public async saveQuizResult(result: any): Promise<void> {
    const offlineResults = await this.getOfflineData<any[]>('quizResults') || [];
    offlineResults.push(result);
    await this.saveOfflineData('quizResults', offlineResults);

    // Add to pending actions for sync when online
    if (!this.isOnline) {
      await this.addPendingAction({
        type: 'SAVE_QUIZ_RESULT',
        payload: result,
        maxRetries: 3,
      });
    }
  }

  // Chat-specific offline functionality
  public async saveChatHistory(messages: any[]): Promise<void> {
    await this.saveOfflineData('chatHistory', messages);
  }

  public async getOfflineChatHistory(): Promise<any[]> {
    return await this.getOfflineData<any[]>('chatHistory') || [];
  }

  public async queueChatMessage(message: string): Promise<void> {
    if (!this.isOnline) {
      await this.addPendingAction({
        type: 'SEND_MESSAGE',
        payload: { message },
        maxRetries: 3,
      });
    }
  }

  // Settings sync
  public async syncUserSettings(settings: any): Promise<void> {
    await this.saveOfflineData('userSettings', settings);
    
    if (!this.isOnline) {
      await this.addPendingAction({
        type: 'SYNC_SETTINGS',
        payload: settings,
        maxRetries: 5,
      });
    }
  }

  public async getOfflineSettings(): Promise<any> {
    return await this.getOfflineData('userSettings');
  }

  // Utility methods
  public getPendingActionsCount(): number {
    return this.pendingActions.length;
  }

  public async getStorageUsage(): Promise<{ used: number; available: number }> {
    try {
      // This is a simplified calculation
      // In a real app, you might want to calculate actual storage usage
      const keys = await AsyncStorage.getAllKeys();
      const offlineKeys = keys.filter(key => key.startsWith('@offline_'));
      
      let totalSize = 0;
      for (const key of offlineKeys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          totalSize += value.length;
        }
      }

      return {
        used: totalSize,
        available: 50 * 1024 * 1024, // Assume 50MB available (simplified)
      };
    } catch (error) {
      console.error('Error calculating storage usage:', error);
      return { used: 0, available: 0 };
    }
  }

  public async cleanupOldData(maxAge: number = 30 * 24 * 60 * 60 * 1000): Promise<void> {
    try {
      const cutoffDate = new Date(Date.now() - maxAge);
      
      // Clean up old quiz results
      const quizResults = await this.getOfflineData<any[]>('quizResults') || [];
      const filteredResults = quizResults.filter(result => 
        new Date(result.timestamp) > cutoffDate
      );
      await this.saveOfflineData('quizResults', filteredResults);

      // Clean up old pending actions
      this.pendingActions = this.pendingActions.filter(action => 
        action.timestamp > cutoffDate
      );
      await this.savePendingActions();
      
    } catch (error) {
      console.error('Error cleaning up old data:', error);
    }
  }
}

export const OfflineManager = new OfflineManagerClass();
