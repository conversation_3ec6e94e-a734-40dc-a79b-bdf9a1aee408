# Security Guide

This document outlines the security measures, best practices, and guidelines implemented in the Test Me (اختبرني) application.

## Table of Contents

- [Security Overview](#security-overview)
- [Data Protection](#data-protection)
- [API Security](#api-security)
- [Authentication & Authorization](#authentication--authorization)
- [Input Validation](#input-validation)
- [Error Handling](#error-handling)
- [Privacy Compliance](#privacy-compliance)
- [Security Testing](#security-testing)
- [Incident Response](#incident-response)

## Security Overview

### Security Principles

1. **Defense in Depth**: Multiple layers of security controls
2. **Least Privilege**: Minimal access rights for users and processes
3. **Fail Secure**: System fails to a secure state
4. **Privacy by Design**: Privacy considerations built into the system
5. **Transparency**: Clear communication about data handling

### Security Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   API Gateway   │    │  External APIs  │
│                 │    │                 │    │                 │
│ • Input Valid.  │◄──►│ • Rate Limiting │◄──►│ • Gemini AI     │
│ • Data Encrypt. │    │ • Auth Checks   │    │ • File Services │
│ • Secure Store  │    │ • Request Log   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Local Storage   │    │   Monitoring    │    │   Compliance    │
│                 │    │                 │    │                 │
│ • SQLite Encrypt│    │ • Error Track   │    │ • GDPR/CCPA     │
│ • AsyncStorage  │    │ • Audit Logs    │    │ • Data Retention│
│ • Keychain      │    │ • Alerts        │    │ • User Rights   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Data Protection

### Data Classification

#### Sensitive Data
- **API Keys**: Encrypted storage in secure keychain
- **User Preferences**: Encrypted local storage
- **Quiz Content**: Temporary storage with automatic cleanup
- **Chat History**: Local storage with user control

#### Public Data
- **App Configuration**: Non-sensitive settings
- **Static Content**: Images, translations, help text
- **Analytics**: Anonymized usage data

### Encryption

#### Data at Rest
```typescript
// Secure storage for sensitive data
import * as SecureStore from 'expo-secure-store';

export class SecureStorageService {
  static async setSecureItem(key: string, value: string): Promise<void> {
    try {
      await SecureStore.setItemAsync(key, value, {
        keychainService: 'com.testme.app.keychain',
        requireAuthentication: true,
      });
    } catch (error) {
      throw new SecurityError('Failed to store secure item', error);
    }
  }

  static async getSecureItem(key: string): Promise<string | null> {
    try {
      return await SecureStore.getItemAsync(key, {
        keychainService: 'com.testme.app.keychain',
        requireAuthentication: true,
      });
    } catch (error) {
      console.error('Failed to retrieve secure item:', error);
      return null;
    }
  }
}
```

#### Data in Transit
- **HTTPS Only**: All network communications use TLS 1.3+
- **Certificate Pinning**: Prevent man-in-the-middle attacks
- **Request Signing**: API requests signed with HMAC

```typescript
// Network security configuration
export const networkConfig = {
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'X-App-Version': APP_VERSION,
    'X-Request-ID': generateRequestId(),
  },
  httpsAgent: {
    rejectUnauthorized: true,
    secureProtocol: 'TLSv1_3_method',
  },
};
```

### Data Minimization

1. **Collection**: Only collect necessary data
2. **Processing**: Process only for stated purposes
3. **Storage**: Store only required data
4. **Retention**: Automatic data cleanup policies

```typescript
// Data retention policy
export class DataRetentionService {
  static async cleanupExpiredData(): Promise<void> {
    const retentionPeriods = {
      chatHistory: 30 * 24 * 60 * 60 * 1000, // 30 days
      quizCache: 7 * 24 * 60 * 60 * 1000,    // 7 days
      errorLogs: 90 * 24 * 60 * 60 * 1000,   // 90 days
      analytics: 365 * 24 * 60 * 60 * 1000,  // 1 year
    };

    await Promise.all([
      this.cleanupChatHistory(retentionPeriods.chatHistory),
      this.cleanupQuizCache(retentionPeriods.quizCache),
      this.cleanupErrorLogs(retentionPeriods.errorLogs),
      this.cleanupAnalytics(retentionPeriods.analytics),
    ]);
  }
}
```

## API Security

### Authentication

#### API Key Management
```typescript
export class ApiKeyService {
  private static readonly ENCRYPTION_KEY = 'api_key_encryption';

  static async storeApiKey(provider: string, key: string): Promise<void> {
    // Encrypt API key before storage
    const encryptedKey = await this.encryptApiKey(key);
    await SecureStorageService.setSecureItem(`api_key_${provider}`, encryptedKey);
  }

  static async getApiKey(provider: string): Promise<string | null> {
    const encryptedKey = await SecureStorageService.getSecureItem(`api_key_${provider}`);
    if (!encryptedKey) return null;
    
    return await this.decryptApiKey(encryptedKey);
  }

  private static async encryptApiKey(key: string): Promise<string> {
    // Implementation using crypto library
    // Return encrypted key
  }
}
```

#### Request Authentication
```typescript
// Request signing for API calls
export class RequestSigner {
  static signRequest(request: ApiRequest): ApiRequest {
    const timestamp = Date.now().toString();
    const nonce = generateNonce();
    const signature = this.generateSignature(request, timestamp, nonce);

    return {
      ...request,
      headers: {
        ...request.headers,
        'X-Timestamp': timestamp,
        'X-Nonce': nonce,
        'X-Signature': signature,
      },
    };
  }

  private static generateSignature(
    request: ApiRequest,
    timestamp: string,
    nonce: string
  ): string {
    const payload = `${request.method}${request.url}${request.body}${timestamp}${nonce}`;
    return hmacSha256(payload, API_SECRET);
  }
}
```

### Rate Limiting

```typescript
export class RateLimiter {
  private static requests = new Map<string, number[]>();
  private static readonly WINDOW_SIZE = 60 * 1000; // 1 minute
  private static readonly MAX_REQUESTS = 60; // 60 requests per minute

  static async checkRateLimit(identifier: string): Promise<boolean> {
    const now = Date.now();
    const windowStart = now - this.WINDOW_SIZE;
    
    // Get existing requests for this identifier
    const requests = this.requests.get(identifier) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => time > windowStart);
    
    // Check if under limit
    if (validRequests.length >= this.MAX_REQUESTS) {
      throw new RateLimitError('Rate limit exceeded');
    }
    
    // Add current request
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
    
    return true;
  }
}
```

## Authentication & Authorization

### User Authentication

```typescript
export class AuthService {
  static async authenticateUser(credentials: UserCredentials): Promise<AuthResult> {
    try {
      // Validate credentials format
      this.validateCredentials(credentials);
      
      // Rate limit authentication attempts
      await RateLimiter.checkRateLimit(`auth_${credentials.identifier}`);
      
      // Authenticate with backend
      const result = await this.performAuthentication(credentials);
      
      // Store authentication token securely
      await SecureStorageService.setSecureItem('auth_token', result.token);
      
      return result;
    } catch (error) {
      await this.logAuthenticationAttempt(credentials.identifier, false);
      throw error;
    }
  }

  static async validateSession(): Promise<boolean> {
    try {
      const token = await SecureStorageService.getSecureItem('auth_token');
      if (!token) return false;
      
      // Validate token with backend
      return await this.validateToken(token);
    } catch (error) {
      console.error('Session validation failed:', error);
      return false;
    }
  }
}
```

### Permission Management

```typescript
export class PermissionService {
  static async requestPermission(permission: Permission): Promise<boolean> {
    try {
      // Check if permission already granted
      const status = await this.checkPermissionStatus(permission);
      if (status === 'granted') return true;
      
      // Show permission rationale if needed
      if (status === 'denied') {
        await this.showPermissionRationale(permission);
      }
      
      // Request permission
      const result = await this.requestSystemPermission(permission);
      
      // Log permission request
      await AnalyticsService.trackEvent('permission_requested', {
        permission: permission.type,
        result: result.status,
      });
      
      return result.status === 'granted';
    } catch (error) {
      await ErrorHandler.handleError(error, {
        context: 'PermissionService.requestPermission',
        permission: permission.type,
      });
      return false;
    }
  }
}
```

## Input Validation

### Client-Side Validation

```typescript
export class InputValidator {
  static validateQuizInput(input: QuizInput): ValidationResult {
    const errors: string[] = [];
    
    // Validate question count
    if (!input.questionCount || input.questionCount < 1 || input.questionCount > 50) {
      errors.push('Question count must be between 1 and 50');
    }
    
    // Validate difficulty
    if (!['easy', 'medium', 'hard'].includes(input.difficulty)) {
      errors.push('Invalid difficulty level');
    }
    
    // Validate text content
    if (!input.text || input.text.length < 100) {
      errors.push('Text content must be at least 100 characters');
    }
    
    // Sanitize text content
    const sanitizedText = this.sanitizeText(input.text);
    
    return {
      isValid: errors.length === 0,
      errors,
      sanitizedInput: { ...input, text: sanitizedText },
    };
  }

  static sanitizeText(text: string): string {
    // Remove potentially dangerous content
    return text
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }
}
```

### File Upload Security

```typescript
export class FileSecurityService {
  private static readonly ALLOWED_TYPES = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'image/webp',
  ];
  
  private static readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

  static async validateFile(file: FileUpload): Promise<ValidationResult> {
    const errors: string[] = [];
    
    // Check file type
    if (!this.ALLOWED_TYPES.includes(file.mimeType)) {
      errors.push('File type not allowed');
    }
    
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      errors.push('File size exceeds limit');
    }
    
    // Check file content (magic bytes)
    const isValidContent = await this.validateFileContent(file);
    if (!isValidContent) {
      errors.push('File content validation failed');
    }
    
    // Scan for malware (if service available)
    const isSafe = await this.scanForMalware(file);
    if (!isSafe) {
      errors.push('File failed security scan');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private static async validateFileContent(file: FileUpload): Promise<boolean> {
    // Validate file magic bytes match declared MIME type
    const buffer = await file.arrayBuffer();
    const magicBytes = new Uint8Array(buffer.slice(0, 8));
    
    return this.checkMagicBytes(magicBytes, file.mimeType);
  }
}
```

## Error Handling

### Secure Error Messages

```typescript
export class SecurityErrorHandler {
  static handleSecurityError(error: Error, context: ErrorContext): UserError {
    // Log detailed error for developers
    console.error('Security error:', {
      error: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
    });
    
    // Return generic error message to user
    return new UserError(
      'A security error occurred. Please try again.',
      'SECURITY_ERROR',
      { canRetry: true }
    );
  }

  static sanitizeErrorMessage(message: string): string {
    // Remove sensitive information from error messages
    return message
      .replace(/api[_-]?key[s]?[:\s=]+[a-zA-Z0-9]+/gi, 'API_KEY_REDACTED')
      .replace(/token[s]?[:\s=]+[a-zA-Z0-9]+/gi, 'TOKEN_REDACTED')
      .replace(/password[s]?[:\s=]+\S+/gi, 'PASSWORD_REDACTED')
      .replace(/\/[a-zA-Z0-9\/]+/g, '/PATH_REDACTED');
  }
}
```

## Privacy Compliance

### GDPR Compliance

```typescript
export class PrivacyService {
  static async exportUserData(userId: string): Promise<UserDataExport> {
    // Collect all user data
    const userData = await this.collectUserData(userId);
    
    // Format for export
    return {
      userId,
      exportDate: new Date().toISOString(),
      data: {
        profile: userData.profile,
        quizHistory: userData.quizHistory,
        settings: userData.settings,
        analytics: userData.analytics,
      },
    };
  }

  static async deleteUserData(userId: string): Promise<void> {
    // Delete all user data
    await Promise.all([
      DatabaseService.deleteUserQuizzes(userId),
      AsyncStorageService.clearUserData(userId),
      AnalyticsService.deleteUserData(userId),
      CacheService.clearUserCache(userId),
    ]);
    
    // Log data deletion
    await this.logDataDeletion(userId);
  }

  static async getPrivacySettings(userId: string): Promise<PrivacySettings> {
    return {
      dataCollection: await this.getDataCollectionSettings(userId),
      analytics: await this.getAnalyticsSettings(userId),
      marketing: await this.getMarketingSettings(userId),
    };
  }
}
```

### Data Processing Consent

```typescript
export class ConsentService {
  static async requestConsent(consentType: ConsentType): Promise<boolean> {
    // Show consent dialog
    const consent = await this.showConsentDialog(consentType);
    
    // Record consent decision
    await this.recordConsent(consentType, consent);
    
    // Update privacy settings
    await this.updatePrivacySettings(consentType, consent);
    
    return consent;
  }

  static async withdrawConsent(consentType: ConsentType): Promise<void> {
    // Record consent withdrawal
    await this.recordConsentWithdrawal(consentType);
    
    // Stop related data processing
    await this.stopDataProcessing(consentType);
    
    // Delete related data if required
    await this.deleteConsentData(consentType);
  }
}
```

## Security Testing

### Automated Security Tests

```typescript
// Security test suite
describe('Security Tests', () => {
  describe('Input Validation', () => {
    it('should reject malicious input', () => {
      const maliciousInputs = [
        '<script>alert("xss")</script>',
        'javascript:alert("xss")',
        '../../etc/passwd',
        'DROP TABLE users;',
      ];
      
      maliciousInputs.forEach(input => {
        expect(() => InputValidator.validateText(input)).toThrow();
      });
    });
  });

  describe('API Security', () => {
    it('should require authentication for protected endpoints', async () => {
      const response = await fetch('/api/protected', {
        method: 'GET',
      });
      
      expect(response.status).toBe(401);
    });
  });

  describe('Data Encryption', () => {
    it('should encrypt sensitive data', async () => {
      const sensitiveData = 'api_key_12345';
      const encrypted = await SecureStorageService.encrypt(sensitiveData);
      
      expect(encrypted).not.toBe(sensitiveData);
      expect(encrypted).toMatch(/^[a-zA-Z0-9+/]+=*$/); // Base64 pattern
    });
  });
});
```

### Penetration Testing

1. **Regular Security Audits**: Quarterly security assessments
2. **Vulnerability Scanning**: Automated scanning of dependencies
3. **Code Review**: Security-focused code reviews
4. **Third-Party Testing**: Annual penetration testing

## Incident Response

### Security Incident Response Plan

1. **Detection**: Automated monitoring and alerting
2. **Assessment**: Evaluate severity and impact
3. **Containment**: Isolate affected systems
4. **Eradication**: Remove security threats
5. **Recovery**: Restore normal operations
6. **Lessons Learned**: Post-incident review

### Incident Classification

- **Critical**: Data breach, system compromise
- **High**: Unauthorized access, service disruption
- **Medium**: Suspicious activity, policy violations
- **Low**: Minor security issues, false positives

### Contact Information

- **Security Team**: <EMAIL>
- **Emergency**: +1-XXX-XXX-XXXX
- **Bug Bounty**: <EMAIL>

## Security Monitoring

### Continuous Monitoring

```typescript
export class SecurityMonitor {
  static async monitorSecurityEvents(): Promise<void> {
    // Monitor authentication failures
    await this.monitorAuthFailures();
    
    // Monitor API abuse
    await this.monitorApiAbuse();
    
    // Monitor data access patterns
    await this.monitorDataAccess();
    
    // Monitor system integrity
    await this.monitorSystemIntegrity();
  }

  static async alertSecurityTeam(event: SecurityEvent): Promise<void> {
    // Send immediate alert for critical events
    if (event.severity === 'critical') {
      await this.sendImmediateAlert(event);
    }
    
    // Log all security events
    await this.logSecurityEvent(event);
    
    // Update security dashboard
    await this.updateSecurityDashboard(event);
  }
}
```

For security concerns or to report vulnerabilities, <NAME_EMAIL>.
