import { AsyncStorageService } from '../storage/asyncStorage';

// Event Types
export enum AnalyticsEvent {
  // User Actions
  APP_OPENED = 'app_opened',
  SCREEN_VIEWED = 'screen_viewed',
  BUTTON_CLICKED = 'button_clicked',
  
  // Quiz Events
  QUIZ_STARTED = 'quiz_started',
  QUIZ_COMPLETED = 'quiz_completed',
  QUESTION_ANSWERED = 'question_answered',
  
  // File Events
  FILE_UPLOADED = 'file_uploaded',
  FILE_PROCESSED = 'file_processed',
  
  // Chat Events
  CHAT_MESSAGE_SENT = 'chat_message_sent',
  CHAT_SESSION_STARTED = 'chat_session_started',
  
  // Settings Events
  LANGUAGE_CHANGED = 'language_changed',
  API_KEY_ADDED = 'api_key_added',
  
  // Performance Events
  PERFORMANCE_METRIC = 'performance_metric',
  ERROR_OCCURRED = 'error_occurred',
}

// Analytics Event Interface
export interface AnalyticsEventData {
  event: AnalyticsEvent;
  timestamp: string;
  properties?: Record<string, any>;
  userId?: string;
  sessionId?: string;
  screen?: string;
  duration?: number;
}

// Performance Metrics
export interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'percentage';
  timestamp: string;
  context?: Record<string, any>;
}

// User Session
export interface UserSession {
  id: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  screenViews: number;
  actions: number;
  errors: number;
}

// Analytics Service
export class AnalyticsService {
  private static events: AnalyticsEventData[] = [];
  private static currentSession: UserSession | null = null;
  private static performanceMetrics: PerformanceMetric[] = [];
  private static maxEventStorage = 1000;

  // Initialize analytics
  static async initialize(): Promise<void> {
    await this.loadStoredData();
    await this.startSession();
  }

  // Start a new session
  static async startSession(): Promise<void> {
    this.currentSession = {
      id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      startTime: new Date().toISOString(),
      screenViews: 0,
      actions: 0,
      errors: 0,
    };

    await this.trackEvent(AnalyticsEvent.APP_OPENED, {
      sessionId: this.currentSession.id,
    });
  }

  // End current session
  static async endSession(): Promise<void> {
    if (this.currentSession) {
      const endTime = new Date().toISOString();
      const duration = new Date(endTime).getTime() - new Date(this.currentSession.startTime).getTime();
      
      this.currentSession.endTime = endTime;
      this.currentSession.duration = duration;

      await this.saveSessionData();
    }
  }

  // Track an event
  static async trackEvent(
    event: AnalyticsEvent,
    properties?: Record<string, any>,
    screen?: string
  ): Promise<void> {
    const eventData: AnalyticsEventData = {
      event,
      timestamp: new Date().toISOString(),
      properties,
      sessionId: this.currentSession?.id,
      screen,
    };

    this.events.unshift(eventData);

    // Update session counters
    if (this.currentSession) {
      if (event === AnalyticsEvent.SCREEN_VIEWED) {
        this.currentSession.screenViews++;
      } else if (event === AnalyticsEvent.ERROR_OCCURRED) {
        this.currentSession.errors++;
      } else {
        this.currentSession.actions++;
      }
    }

    // Keep events manageable
    if (this.events.length > this.maxEventStorage) {
      this.events = this.events.slice(0, this.maxEventStorage);
    }

    await this.saveEventData();
  }

  // Track screen view
  static async trackScreenView(screenName: string, properties?: Record<string, any>): Promise<void> {
    await this.trackEvent(AnalyticsEvent.SCREEN_VIEWED, {
      screen_name: screenName,
      ...properties,
    }, screenName);
  }

  // Track button click
  static async trackButtonClick(
    buttonName: string,
    screen: string,
    properties?: Record<string, any>
  ): Promise<void> {
    await this.trackEvent(AnalyticsEvent.BUTTON_CLICKED, {
      button_name: buttonName,
      screen,
      ...properties,
    }, screen);
  }

  // Track quiz events
  static async trackQuizStarted(quizId: string, questionCount: number): Promise<void> {
    await this.trackEvent(AnalyticsEvent.QUIZ_STARTED, {
      quiz_id: quizId,
      question_count: questionCount,
    });
  }

  static async trackQuizCompleted(
    quizId: string,
    score: number,
    totalQuestions: number,
    duration: number
  ): Promise<void> {
    await this.trackEvent(AnalyticsEvent.QUIZ_COMPLETED, {
      quiz_id: quizId,
      score,
      total_questions: totalQuestions,
      duration,
      score_percentage: Math.round((score / totalQuestions) * 100),
    });
  }

  // Track performance metrics
  static async trackPerformance(
    name: string,
    value: number,
    unit: PerformanceMetric['unit'],
    context?: Record<string, any>
  ): Promise<void> {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: new Date().toISOString(),
      context,
    };

    this.performanceMetrics.unshift(metric);

    // Keep metrics manageable
    if (this.performanceMetrics.length > 500) {
      this.performanceMetrics = this.performanceMetrics.slice(0, 500);
    }

    await this.trackEvent(AnalyticsEvent.PERFORMANCE_METRIC, {
      metric_name: name,
      metric_value: value,
      metric_unit: unit,
      ...context,
    });
  }

  // Measure and track execution time
  static async measureExecutionTime<T>(
    name: string,
    fn: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      
      await this.trackPerformance(name, duration, 'ms', {
        status: 'success',
        ...context,
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      await this.trackPerformance(name, duration, 'ms', {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        ...context,
      });
      
      throw error;
    }
  }

  // Get analytics summary
  static getAnalyticsSummary(): {
    totalEvents: number;
    eventsByType: Record<string, number>;
    currentSession: UserSession | null;
    averageSessionDuration: number;
    topScreens: Array<{ screen: string; views: number }>;
    performanceMetrics: {
      averageLoadTime: number;
      errorRate: number;
      totalMetrics: number;
    };
  } {
    const eventsByType = this.events.reduce((acc, event) => {
      acc[event.event] = (acc[event.event] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const screenViews = this.events
      .filter(event => event.event === AnalyticsEvent.SCREEN_VIEWED)
      .reduce((acc, event) => {
        const screen = event.properties?.screen_name || 'Unknown';
        acc[screen] = (acc[screen] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    const topScreens = Object.entries(screenViews)
      .map(([screen, views]) => ({ screen, views }))
      .sort((a, b) => b.views - a.views)
      .slice(0, 5);

    const loadTimeMetrics = this.performanceMetrics.filter(m => m.name.includes('load'));
    const averageLoadTime = loadTimeMetrics.length > 0
      ? loadTimeMetrics.reduce((sum, m) => sum + m.value, 0) / loadTimeMetrics.length
      : 0;

    const errorEvents = this.events.filter(event => event.event === AnalyticsEvent.ERROR_OCCURRED);
    const errorRate = this.events.length > 0 ? (errorEvents.length / this.events.length) * 100 : 0;

    return {
      totalEvents: this.events.length,
      eventsByType,
      currentSession: this.currentSession,
      averageSessionDuration: 0, // Would calculate from stored sessions
      topScreens,
      performanceMetrics: {
        averageLoadTime,
        errorRate,
        totalMetrics: this.performanceMetrics.length,
      },
    };
  }

  // Export analytics data
  static async exportAnalyticsData(): Promise<string> {
    const data = {
      events: this.events,
      performanceMetrics: this.performanceMetrics,
      currentSession: this.currentSession,
      summary: this.getAnalyticsSummary(),
      exportedAt: new Date().toISOString(),
    };

    return JSON.stringify(data, null, 2);
  }

  // Clear analytics data
  static async clearAnalyticsData(): Promise<void> {
    this.events = [];
    this.performanceMetrics = [];
    this.currentSession = null;
    
    await AsyncStorageService.removeItem('analytics_events');
    await AsyncStorageService.removeItem('analytics_metrics');
    await AsyncStorageService.removeItem('analytics_sessions');
  }

  // Private methods for data persistence
  private static async loadStoredData(): Promise<void> {
    try {
      const [eventsData, metricsData] = await Promise.all([
        AsyncStorageService.getItem('analytics_events'),
        AsyncStorageService.getItem('analytics_metrics'),
      ]);

      if (eventsData) {
        this.events = JSON.parse(eventsData);
      }

      if (metricsData) {
        this.performanceMetrics = JSON.parse(metricsData);
      }
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    }
  }

  private static async saveEventData(): Promise<void> {
    try {
      await AsyncStorageService.setItem('analytics_events', JSON.stringify(this.events));
    } catch (error) {
      console.error('Failed to save analytics events:', error);
    }
  }

  private static async saveSessionData(): Promise<void> {
    try {
      if (this.currentSession) {
        const sessions = await this.getStoredSessions();
        sessions.unshift(this.currentSession);
        
        // Keep only last 50 sessions
        const limitedSessions = sessions.slice(0, 50);
        
        await AsyncStorageService.setItem('analytics_sessions', JSON.stringify(limitedSessions));
      }
    } catch (error) {
      console.error('Failed to save session data:', error);
    }
  }

  private static async getStoredSessions(): Promise<UserSession[]> {
    try {
      const data = await AsyncStorageService.getItem('analytics_sessions');
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to load sessions:', error);
      return [];
    }
  }
}
