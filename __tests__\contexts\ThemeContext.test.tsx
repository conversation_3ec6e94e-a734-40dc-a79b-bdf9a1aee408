import React from 'react';
import { render, act, waitFor } from '@testing-library/react-native';
import { Text } from 'react-native';
import { ThemeProvider, useTheme } from '@/contexts/ThemeContext';
import { AsyncStorageService } from '@/services/storage/asyncStorage';
import { AnalyticsService } from '@/services/analytics/analyticsService';
import { lightTheme, darkTheme } from '@/constants/themes';

// Mock dependencies
jest.mock('@/services/storage/asyncStorage');
jest.mock('@/services/analytics/analyticsService');

const MockedAsyncStorageService = AsyncStorageService as jest.Mocked<typeof AsyncStorageService>;
const MockedAnalyticsService = AnalyticsService as jest.Mocked<typeof AnalyticsService>;

// Test component that uses the theme context
const TestComponent: React.FC = () => {
  const { theme, isDarkMode, toggleTheme, isLoading } = useTheme();
  
  return (
    <>
      <Text testID="theme-mode">{isDarkMode ? 'dark' : 'light'}</Text>
      <Text testID="theme-primary">{theme.colors.primary}</Text>
      <Text testID="loading-state">{isLoading ? 'loading' : 'loaded'}</Text>
      <Text testID="toggle-theme" onPress={toggleTheme}>Toggle</Text>
    </>
  );
};

describe('ThemeContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    MockedAsyncStorageService.getItem.mockResolvedValue(null);
    MockedAsyncStorageService.setItem.mockResolvedValue();
    MockedAnalyticsService.trackEvent.mockResolvedValue();
  });

  describe('Provider Initialization', () => {
    it('should initialize with light theme by default', async () => {
      const { getByTestId } = render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      await waitFor(() => {
        expect(getByTestId('loading-state').children[0]).toBe('loaded');
      });

      expect(getByTestId('theme-mode').children[0]).toBe('light');
      expect(getByTestId('theme-primary').children[0]).toBe(lightTheme.colors.primary);
    });

    it('should initialize with provided initial theme', async () => {
      const { getByTestId } = render(
        <ThemeProvider initialTheme="dark">
          <TestComponent />
        </ThemeProvider>
      );

      await waitFor(() => {
        expect(getByTestId('loading-state').children[0]).toBe('loaded');
      });

      expect(getByTestId('theme-mode').children[0]).toBe('dark');
      expect(getByTestId('theme-primary').children[0]).toBe(darkTheme.colors.primary);
    });

    it('should load saved theme from storage', async () => {
      MockedAsyncStorageService.getItem.mockResolvedValue('dark');

      const { getByTestId } = render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      await waitFor(() => {
        expect(getByTestId('loading-state').children[0]).toBe('loaded');
      });

      expect(getByTestId('theme-mode').children[0]).toBe('dark');
      expect(MockedAsyncStorageService.getItem).toHaveBeenCalledWith('app_theme_mode');
    });

    it('should handle storage errors gracefully', async () => {
      MockedAsyncStorageService.getItem.mockRejectedValue(new Error('Storage error'));

      const { getByTestId } = render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      await waitFor(() => {
        expect(getByTestId('loading-state').children[0]).toBe('loaded');
      });

      // Should fall back to light theme
      expect(getByTestId('theme-mode').children[0]).toBe('light');
    });
  });

  describe('Theme Toggling', () => {
    it('should toggle from light to dark', async () => {
      const { getByTestId } = render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      await waitFor(() => {
        expect(getByTestId('loading-state').children[0]).toBe('loaded');
      });

      expect(getByTestId('theme-mode').children[0]).toBe('light');

      await act(async () => {
        getByTestId('toggle-theme').props.onPress();
      });

      await waitFor(() => {
        expect(getByTestId('theme-mode').children[0]).toBe('dark');
      });

      expect(getByTestId('theme-primary').children[0]).toBe(darkTheme.colors.primary);
    });

    it('should toggle from dark to light', async () => {
      const { getByTestId } = render(
        <ThemeProvider initialTheme="dark">
          <TestComponent />
        </ThemeProvider>
      );

      await waitFor(() => {
        expect(getByTestId('loading-state').children[0]).toBe('loaded');
      });

      expect(getByTestId('theme-mode').children[0]).toBe('dark');

      await act(async () => {
        getByTestId('toggle-theme').props.onPress();
      });

      await waitFor(() => {
        expect(getByTestId('theme-mode').children[0]).toBe('light');
      });

      expect(getByTestId('theme-primary').children[0]).toBe(lightTheme.colors.primary);
    });

    it('should persist theme changes to storage', async () => {
      const { getByTestId } = render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      await waitFor(() => {
        expect(getByTestId('loading-state').children[0]).toBe('loaded');
      });

      await act(async () => {
        getByTestId('toggle-theme').props.onPress();
      });

      await waitFor(() => {
        expect(MockedAsyncStorageService.setItem).toHaveBeenCalledWith(
          'app_theme_mode',
          'dark'
        );
      });
    });

    it('should track analytics on theme change', async () => {
      const { getByTestId } = render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      await waitFor(() => {
        expect(getByTestId('loading-state').children[0]).toBe('loaded');
      });

      await act(async () => {
        getByTestId('toggle-theme').props.onPress();
      });

      await waitFor(() => {
        expect(MockedAnalyticsService.trackEvent).toHaveBeenCalledWith(
          'theme_changed',
          expect.objectContaining({
            from_theme: 'light',
            to_theme: 'dark',
            method: 'manual',
          })
        );
      });
    });

    it('should handle storage errors during toggle', async () => {
      MockedAsyncStorageService.setItem.mockRejectedValue(new Error('Storage error'));

      const { getByTestId } = render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      await waitFor(() => {
        expect(getByTestId('loading-state').children[0]).toBe('loaded');
      });

      await act(async () => {
        getByTestId('toggle-theme').props.onPress();
      });

      // Theme should still change even if storage fails
      await waitFor(() => {
        expect(getByTestId('theme-mode').children[0]).toBe('dark');
      });
    });

    it('should handle analytics errors during toggle', async () => {
      MockedAnalyticsService.trackEvent.mockRejectedValue(new Error('Analytics error'));

      const { getByTestId } = render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      await waitFor(() => {
        expect(getByTestId('loading-state').children[0]).toBe('loaded');
      });

      await act(async () => {
        getByTestId('toggle-theme').props.onPress();
      });

      // Theme should still change even if analytics fails
      await waitFor(() => {
        expect(getByTestId('theme-mode').children[0]).toBe('dark');
      });
    });
  });

  describe('Loading State', () => {
    it('should show loading state during initialization', () => {
      MockedAsyncStorageService.getItem.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve('dark'), 100))
      );

      const { getByTestId } = render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      expect(getByTestId('loading-state').children[0]).toBe('loading');
    });

    it('should clear loading state after initialization', async () => {
      const { getByTestId } = render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      await waitFor(() => {
        expect(getByTestId('loading-state').children[0]).toBe('loaded');
      });
    });
  });

  describe('useThemedStyles Hook', () => {
    it('should provide themed styles', async () => {
      const TestStyledComponent: React.FC = () => {
        const { useThemedStyles } = useTheme();
        const styles = useThemedStyles((theme) => ({
          container: {
            backgroundColor: theme.colors.background,
            padding: theme.spacing.md,
          },
        }));

        return (
          <Text testID="styled-bg">{styles.container.backgroundColor}</Text>
        );
      };

      const { getByTestId } = render(
        <ThemeProvider>
          <TestStyledComponent />
        </ThemeProvider>
      );

      await waitFor(() => {
        expect(getByTestId('styled-bg').children[0]).toBe(lightTheme.colors.background);
      });
    });

    it('should update styles when theme changes', async () => {
      const TestStyledComponent: React.FC = () => {
        const { useThemedStyles, toggleTheme } = useTheme();
        const styles = useThemedStyles((theme) => ({
          container: {
            backgroundColor: theme.colors.background,
          },
        }));

        return (
          <>
            <Text testID="styled-bg">{styles.container.backgroundColor}</Text>
            <Text testID="toggle" onPress={toggleTheme}>Toggle</Text>
          </>
        );
      };

      const { getByTestId } = render(
        <ThemeProvider>
          <TestStyledComponent />
        </ThemeProvider>
      );

      await waitFor(() => {
        expect(getByTestId('styled-bg').children[0]).toBe(lightTheme.colors.background);
      });

      await act(async () => {
        getByTestId('toggle').props.onPress();
      });

      await waitFor(() => {
        expect(getByTestId('styled-bg').children[0]).toBe(darkTheme.colors.background);
      });
    });
  });

  describe('Error Boundaries', () => {
    it('should throw error when used outside provider', () => {
      const TestComponentWithoutProvider: React.FC = () => {
        useTheme();
        return <Text>Test</Text>;
      };

      expect(() => {
        render(<TestComponentWithoutProvider />);
      }).toThrow('useTheme must be used within a ThemeProvider');
    });
  });
});
