# API Documentation

This document provides comprehensive documentation for all services and APIs used in the Test Me application.

## Table of Contents

- [Core Services](#core-services)
- [AI Services](#ai-services)
- [Storage Services](#storage-services)
- [Analytics Services](#analytics-services)
- [Cache Services](#cache-services)
- [Error Handling](#error-handling)
- [Utility Functions](#utility-functions)

## Core Services

### GeminiService

The GeminiService handles all AI-related operations using Google's Gemini API.

#### Methods

##### `generateQuiz(text: string, options: QuizOptions): Promise<Quiz>`

Generates a quiz from the provided text content.

**Parameters:**
- `text` (string): The source text to generate questions from
- `options` (QuizOptions): Configuration options for quiz generation

**Returns:** Promise<Quiz> - The generated quiz object

**Example:**
```typescript
import { GeminiService } from '@/services/ai/geminiService';

const quiz = await GeminiService.generateQuiz(
  "React Native is a framework for building mobile applications...",
  {
    questionCount: 10,
    difficulty: 'medium',
    questionTypes: ['multiple_choice', 'true_false']
  }
);
```

##### `sendMessage(message: string, context?: ChatContext): Promise<string>`

Sends a message to the AI chat interface.

**Parameters:**
- `message` (string): The user's message
- `context` (ChatContext, optional): Additional context for the conversation

**Returns:** Promise<string> - The AI's response

**Example:**
```typescript
const response = await GeminiService.sendMessage(
  "Explain the concept of React hooks",
  { previousMessages: chatHistory }
);
```

##### `extractTextFromFile(file: FileUpload): Promise<string>`

Extracts text content from uploaded files.

**Parameters:**
- `file` (FileUpload): The uploaded file object

**Returns:** Promise<string> - Extracted text content

**Example:**
```typescript
const extractedText = await GeminiService.extractTextFromFile(uploadedFile);
```

### DatabaseService

Handles all local database operations using SQLite.

#### Methods

##### `saveQuiz(quiz: Quiz): Promise<string>`

Saves a quiz to the local database.

**Parameters:**
- `quiz` (Quiz): The quiz object to save

**Returns:** Promise<string> - The saved quiz ID

##### `getQuizHistory(): Promise<QuizHistoryItem[]>`

Retrieves the user's quiz history.

**Returns:** Promise<QuizHistoryItem[]> - Array of quiz history items

##### `saveQuizResult(result: QuizResult): Promise<void>`

Saves quiz completion results.

**Parameters:**
- `result` (QuizResult): The quiz result to save

**Example:**
```typescript
await DatabaseService.saveQuizResult({
  quizId: 'quiz_123',
  score: 85,
  totalQuestions: 10,
  completedAt: new Date(),
  answers: userAnswers
});
```

## AI Services

### Chat Service

Manages chat interactions with the AI tutor.

#### Methods

##### `createNewSession(): Promise<string>`

Creates a new chat session.

**Returns:** Promise<string> - New session ID

##### `addMessage(message: ChatMessage): Promise<ChatMessage>`

Adds a message to the current chat session.

**Parameters:**
- `message` (ChatMessage): The message to add

**Returns:** Promise<ChatMessage> - The saved message with ID

##### `getQuickActions(): QuickAction[]`

Gets predefined quick action buttons for the chat interface.

**Returns:** QuickAction[] - Array of quick action objects

**Example:**
```typescript
const quickActions = ChatService.getQuickActions();
// Returns actions like "Explain this topic", "Create practice questions", etc.
```

## Storage Services

### AsyncStorageService

Handles persistent storage for user preferences and app state.

#### Methods

##### `setItem(key: string, value: string): Promise<void>`

Stores a value with the specified key.

**Parameters:**
- `key` (string): Storage key
- `value` (string): Value to store

##### `getItem(key: string): Promise<string | null>`

Retrieves a value by key.

**Parameters:**
- `key` (string): Storage key

**Returns:** Promise<string | null> - Stored value or null

##### `removeItem(key: string): Promise<void>`

Removes an item from storage.

**Parameters:**
- `key` (string): Storage key to remove

**Example:**
```typescript
// Save user settings
await AsyncStorageService.setItem('user_settings', JSON.stringify(settings));

// Load user settings
const settingsData = await AsyncStorageService.getItem('user_settings');
const settings = settingsData ? JSON.parse(settingsData) : defaultSettings;
```

## Analytics Services

### AnalyticsService

Tracks user behavior and app performance metrics.

#### Methods

##### `trackEvent(event: AnalyticsEvent, properties?: Record<string, any>): Promise<void>`

Tracks a user event with optional properties.

**Parameters:**
- `event` (AnalyticsEvent): The event type to track
- `properties` (Record<string, any>, optional): Additional event properties

**Example:**
```typescript
await AnalyticsService.trackEvent('quiz_completed', {
  score: 85,
  duration: 300000, // 5 minutes
  questionCount: 10
});
```

##### `trackScreenView(screenName: string, properties?: Record<string, any>): Promise<void>`

Tracks when a user views a screen.

**Parameters:**
- `screenName` (string): Name of the screen
- `properties` (Record<string, any>, optional): Additional properties

##### `measureExecutionTime<T>(name: string, fn: () => Promise<T>): Promise<T>`

Measures and tracks the execution time of a function.

**Parameters:**
- `name` (string): Name of the operation being measured
- `fn` (Function): The function to measure

**Returns:** Promise<T> - The result of the function

**Example:**
```typescript
const quiz = await AnalyticsService.measureExecutionTime(
  'quiz_generation',
  () => GeminiService.generateQuiz(text, options)
);
```

##### `getAnalyticsSummary(): AnalyticsSummary`

Gets a summary of analytics data.

**Returns:** AnalyticsSummary - Summary of tracked events and metrics

## Cache Services

### CacheService

Provides intelligent caching for improved performance.

#### Methods

##### `set<T>(key: string, data: T, ttl?: number, tags?: string[]): Promise<void>`

Stores data in the cache with optional TTL and tags.

**Parameters:**
- `key` (string): Cache key
- `data` (T): Data to cache
- `ttl` (number, optional): Time to live in milliseconds
- `tags` (string[], optional): Tags for cache invalidation

##### `get<T>(key: string): Promise<T | null>`

Retrieves data from the cache.

**Parameters:**
- `key` (string): Cache key

**Returns:** Promise<T | null> - Cached data or null if not found/expired

##### `memoize<T>(fn: Function, keyGenerator?: Function, ttl?: number): T`

Creates a memoized version of a function with caching.

**Parameters:**
- `fn` (Function): Function to memoize
- `keyGenerator` (Function, optional): Custom key generation function
- `ttl` (number, optional): Cache TTL

**Returns:** T - Memoized function

**Example:**
```typescript
// Cache API responses
await CacheService.set('quiz_data_123', quizData, 30 * 60 * 1000); // 30 minutes

// Retrieve cached data
const cachedQuiz = await CacheService.get<Quiz>('quiz_data_123');

// Memoize expensive operations
const memoizedGenerateQuiz = CacheService.memoize(
  GeminiService.generateQuiz,
  (text, options) => `quiz_${hashString(text)}_${JSON.stringify(options)}`,
  60 * 60 * 1000 // 1 hour
);
```

## Error Handling

### ErrorHandler

Provides comprehensive error handling and logging.

#### Methods

##### `handleError(error: Error, context?: ErrorContext, showUserAlert?: boolean): Promise<void>`

Handles and logs errors with optional user notification.

**Parameters:**
- `error` (Error): The error to handle
- `context` (ErrorContext, optional): Additional context about the error
- `showUserAlert` (boolean, optional): Whether to show user-friendly alert

##### `logError(error: Partial<AppError>): Promise<void>`

Logs an error to the error tracking system.

**Parameters:**
- `error` (Partial<AppError>): Error information to log

**Example:**
```typescript
try {
  await riskyOperation();
} catch (error) {
  await ErrorHandler.handleError(error, {
    screen: 'HomeScreen',
    action: 'file_upload',
    metadata: { fileSize: file.size }
  });
}
```

## Utility Functions

### Formatting Utilities

#### `formatDate(date: Date, locale?: string): string`

Formats a date according to the specified locale.

#### `formatScore(score: number, total: number): string`

Formats a quiz score as a percentage.

#### `formatDuration(milliseconds: number): string`

Formats a duration in milliseconds to human-readable format.

### Validation Utilities

#### `validateEmail(email: string): boolean`

Validates an email address format.

#### `validateFileSize(size: number, maxSize: number): boolean`

Validates if a file size is within limits.

### Helper Utilities

#### `generateId(): string`

Generates a unique identifier.

#### `shuffleArray<T>(array: T[]): T[]`

Shuffles an array using Fisher-Yates algorithm.

#### `debounce<T>(func: Function, delay: number): Function`

Creates a debounced version of a function.

**Example:**
```typescript
import { formatScore, validateEmail, generateId } from '@/utils';

// Format quiz score
const scoreText = formatScore(8, 10); // "80%"

// Validate email
const isValid = validateEmail("<EMAIL>"); // true

// Generate unique ID
const quizId = generateId(); // "quiz_1234567890_abc123"
```

## Type Definitions

### Core Types

```typescript
interface Quiz {
  id: string;
  title: string;
  questions: Question[];
  createdAt: Date;
  subject?: string;
}

interface Question {
  id: string;
  text: string;
  type: 'multiple_choice' | 'true_false' | 'open_ended';
  options?: string[];
  correctAnswer: string | number;
  explanation?: string;
}

interface QuizResult {
  quizId: string;
  score: number;
  totalQuestions: number;
  completedAt: Date;
  answers: UserAnswer[];
}
```

### Configuration Types

```typescript
interface QuizOptions {
  questionCount: number;
  difficulty: 'easy' | 'medium' | 'hard';
  questionTypes: QuestionType[];
  subject?: string;
}

interface UserSettings {
  language: 'en' | 'ar';
  apiKeys: ApiKey[];
  notifications: boolean;
  theme: 'light' | 'dark' | 'auto';
}
```

## Error Codes

| Code | Description | Severity |
|------|-------------|----------|
| `NETWORK_ERROR` | Network connectivity issues | Medium |
| `API_ERROR` | API service errors | High |
| `STORAGE_ERROR` | Local storage failures | High |
| `FILE_ERROR` | File processing errors | Medium |
| `VALIDATION_ERROR` | Input validation failures | Low |
| `AUTH_ERROR` | Authentication failures | High |

## Rate Limits

- **Gemini API**: 60 requests per minute
- **File Upload**: 10MB maximum file size
- **Cache**: 50MB maximum cache size
- **Analytics**: 1000 events per session

For more detailed information, see the inline documentation in the source code.
